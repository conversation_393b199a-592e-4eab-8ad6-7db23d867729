#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير اللغات - Language Manager
إدارة التبديل بين اللغة العربية والإنجليزية
"""

import json
import os

class LanguageManager:
    """مدير اللغات للنظام"""
    
    def __init__(self):
        self.current_language = "ar"  # اللغة الافتراضية: العربية
        self.translations = {}
        self.load_translations()
        
    def load_translations(self):
        """تحميل ترجمات النظام"""
        # الترجمات العربية (افتراضية)
        self.translations["ar"] = {
            "app_title": "نظام إدارة التموين العام والقوة العمومية",
            "dashboard": "لوحة التحكم",
            "basic_info": "البيانات الأساسية",
            "equipment": "المعدات",
            "items": "الأصناف",
            "users": "المستخدمين",
            "backup": "النسخ الاحتياطي",
            "login": "تسجيل الدخول",
            "logout": "تسجيل الخروج",
            "username": "اسم المستخدم",
            "password": "كلمة المرور",
            "add": "إضافة",
            "edit": "تعديل",
            "delete": "حذف",
            "save": "حفظ",
            "cancel": "إلغاء",
            "search": "بحث",
            "export": "تصدير",
            "import": "استيراد",
            "print": "طباعة",
            "settings": "الإعدادات",
            "help": "مساعدة",
            "about": "حول البرنامج",
            "exit": "خروج",
            "success": "نجح",
            "error": "خطأ",
            "warning": "تحذير",
            "info": "معلومات",
            "confirm": "تأكيد",
            "yes": "نعم",
            "no": "لا",
            "ok": "موافق",
            "close": "إغلاق"
        }
        
        # الترجمات الإنجليزية
        self.translations["en"] = {
            "app_title": "Supply Management System for Public Forces",
            "dashboard": "Dashboard",
            "basic_info": "Basic Information",
            "equipment": "Equipment",
            "items": "Items",
            "users": "Users",
            "backup": "Backup",
            "login": "Login",
            "logout": "Logout",
            "username": "Username",
            "password": "Password",
            "add": "Add",
            "edit": "Edit",
            "delete": "Delete",
            "save": "Save",
            "cancel": "Cancel",
            "search": "Search",
            "export": "Export",
            "import": "Import",
            "print": "Print",
            "settings": "Settings",
            "help": "Help",
            "about": "About",
            "exit": "Exit",
            "success": "Success",
            "error": "Error",
            "warning": "Warning",
            "info": "Information",
            "confirm": "Confirm",
            "yes": "Yes",
            "no": "No",
            "ok": "OK",
            "close": "Close"
        }
        
    def get_text(self, key, default=None):
        """الحصول على النص المترجم"""
        if default is None:
            default = key
            
        return self.translations.get(self.current_language, {}).get(key, default)
        
    def set_language(self, language_code):
        """تعيين اللغة الحالية"""
        if language_code in self.translations:
            self.current_language = language_code
            return True
        return False
        
    def get_current_language(self):
        """الحصول على اللغة الحالية"""
        return self.current_language
        
    def get_available_languages(self):
        """الحصول على قائمة اللغات المتاحة"""
        return list(self.translations.keys())
        
    def is_rtl(self):
        """التحقق من اتجاه الكتابة (من اليمين لليسار)"""
        rtl_languages = ["ar", "he", "fa", "ur"]
        return self.current_language in rtl_languages
        
    def save_language_preference(self, config_file="config.json"):
        """حفظ تفضيل اللغة"""
        try:
            config = {}
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    
            config['language'] = self.current_language
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"خطأ في حفظ تفضيل اللغة: {e}")
            
    def load_language_preference(self, config_file="config.json"):
        """تحميل تفضيل اللغة"""
        try:
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    language = config.get('language', 'ar')
                    self.set_language(language)
                    
        except Exception as e:
            print(f"خطأ في تحميل تفضيل اللغة: {e}")

# إنشاء مثيل عام لمدير اللغات
language_manager = LanguageManager()
