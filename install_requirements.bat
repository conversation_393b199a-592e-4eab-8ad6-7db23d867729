@echo off
chcp 65001 > nul
title تثبيت متطلبات نظام إدارة التموين

echo ========================================
echo    تثبيت متطلبات نظام إدارة التموين
echo ========================================
echo.

echo جاري التحقق من Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث من https://python.org
    pause
    exit /b 1
)

echo جاري تثبيت المتطلبات الأساسية...
pip install --upgrade pip

echo تثبيت ttkbootstrap...
pip install ttkbootstrap

echo تثبيت matplotlib...
pip install matplotlib

echo تثبيت tkcalendar...
pip install tkcalendar

echo تثبيت pandas...
pip install pandas

echo تثبيت openpyxl...
pip install openpyxl

echo تثبيت reportlab...
pip install reportlab

echo تثبيت xlsxwriter...
pip install xlsxwriter

echo تثبيت python-dateutil...
pip install python-dateutil

echo تثبيت Pillow...
pip install Pillow

echo.
echo تم تثبيت جميع المتطلبات بنجاح!
echo يمكنك الآن تشغيل النظام باستخدام: python main.py
echo.
pause
