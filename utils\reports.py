#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير التقارير - Reports Manager
إنشاء وتصدير التقارير بصيغ مختلفة (PDF, Excel, CSV)
"""

import os
from datetime import datetime
import pandas as pd
from reportlab.lib.pagesizes import A4, letter
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib import colors
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

class ReportsManager:
    """مدير التقارير والتصدير"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.setup_fonts()
        
    def setup_fonts(self):
        """إعداد الخطوط العربية للتقارير"""
        try:
            # محاولة تسجيل خط عربي (يمكن تخصيصه حسب النظام)
            # pdfmetrics.registerFont(TTFont('Arabic', 'path/to/arabic/font.ttf'))
            pass
        except:
            # استخدام الخط الافتراضي إذا لم يتم العثور على خط عربي
            pass
            
    def generate_equipment_report(self, output_path=None, format_type="pdf"):
        """إنشاء تقرير المعدات"""
        try:
            # جلب بيانات المعدات
            equipment_data = self.db_manager.fetch_all("""
                SELECT e.*, u.unit_name 
                FROM equipment e 
                LEFT JOIN military_units u ON e.unit_id = u.id
                ORDER BY e.equipment_name
            """)
            
            if format_type.lower() == "pdf":
                return self._create_equipment_pdf_report(equipment_data, output_path)
            elif format_type.lower() == "excel":
                return self._create_equipment_excel_report(equipment_data, output_path)
            elif format_type.lower() == "csv":
                return self._create_equipment_csv_report(equipment_data, output_path)
                
        except Exception as e:
            raise Exception(f"خطأ في إنشاء تقرير المعدات: {str(e)}")
            
    def generate_items_report(self, output_path=None, format_type="pdf"):
        """إنشاء تقرير الأصناف"""
        try:
            # جلب بيانات الأصناف
            items_data = self.db_manager.fetch_all("""
                SELECT * FROM items 
                ORDER BY item_name
            """)
            
            if format_type.lower() == "pdf":
                return self._create_items_pdf_report(items_data, output_path)
            elif format_type.lower() == "excel":
                return self._create_items_excel_report(items_data, output_path)
            elif format_type.lower() == "csv":
                return self._create_items_csv_report(items_data, output_path)
                
        except Exception as e:
            raise Exception(f"خطأ في إنشاء تقرير الأصناف: {str(e)}")
            
    def generate_stock_report(self, output_path=None, format_type="pdf"):
        """إنشاء تقرير المخزون"""
        try:
            # جلب بيانات المخزون
            stock_data = self.db_manager.fetch_all("""
                SELECT 
                    item_name,
                    category,
                    current_stock,
                    minimum_stock,
                    maximum_stock,
                    unit_of_measure,
                    unit_price,
                    CASE 
                        WHEN current_stock <= minimum_stock THEN 'منخفض'
                        WHEN current_stock >= maximum_stock THEN 'مرتفع'
                        ELSE 'طبيعي'
                    END as stock_status
                FROM items 
                ORDER BY item_name
            """)
            
            if format_type.lower() == "pdf":
                return self._create_stock_pdf_report(stock_data, output_path)
            elif format_type.lower() == "excel":
                return self._create_stock_excel_report(stock_data, output_path)
            elif format_type.lower() == "csv":
                return self._create_stock_csv_report(stock_data, output_path)
                
        except Exception as e:
            raise Exception(f"خطأ في إنشاء تقرير المخزون: {str(e)}")
            
    def _create_equipment_pdf_report(self, data, output_path):
        """إنشاء تقرير PDF للمعدات"""
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"equipment_report_{timestamp}.pdf"
            
        doc = SimpleDocTemplate(output_path, pagesize=A4)
        story = []
        
        # العنوان
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=1  # وسط
        )
        
        title = Paragraph("تقرير المعدات", title_style)
        story.append(title)
        story.append(Spacer(1, 12))
        
        # إنشاء الجدول
        if data:
            table_data = [['اسم المعدة', 'الفئة', 'الحالة', 'الوحدة المسؤولة', 'تاريخ الشراء']]
            
            for item in data:
                table_data.append([
                    item.get('equipment_name', ''),
                    item.get('category', ''),
                    item.get('status', ''),
                    item.get('unit_name', ''),
                    item.get('purchase_date', '')
                ])
                
            table = Table(table_data)
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 14),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(table)
        else:
            story.append(Paragraph("لا توجد بيانات للعرض", styles['Normal']))
            
        doc.build(story)
        return output_path
        
    def _create_equipment_excel_report(self, data, output_path):
        """إنشاء تقرير Excel للمعدات"""
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"equipment_report_{timestamp}.xlsx"
            
        df = pd.DataFrame(data)
        
        # إعادة تسمية الأعمدة
        column_mapping = {
            'equipment_name': 'اسم المعدة',
            'category': 'الفئة',
            'status': 'الحالة',
            'unit_name': 'الوحدة المسؤولة',
            'purchase_date': 'تاريخ الشراء',
            'warranty_expiry': 'انتهاء الضمان',
            'serial_number': 'الرقم التسلسلي'
        }
        
        df = df.rename(columns=column_mapping)
        
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='تقرير المعدات', index=False)
            
        return output_path
        
    def _create_equipment_csv_report(self, data, output_path):
        """إنشاء تقرير CSV للمعدات"""
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"equipment_report_{timestamp}.csv"
            
        df = pd.DataFrame(data)
        df.to_csv(output_path, index=False, encoding='utf-8-sig')
        return output_path
        
    def _create_items_pdf_report(self, data, output_path):
        """إنشاء تقرير PDF للأصناف"""
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"items_report_{timestamp}.pdf"
            
        # منطق مشابه لتقرير المعدات
        return self._create_generic_pdf_report(data, output_path, "تقرير الأصناف")
        
    def _create_items_excel_report(self, data, output_path):
        """إنشاء تقرير Excel للأصناف"""
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"items_report_{timestamp}.xlsx"
            
        df = pd.DataFrame(data)
        
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='تقرير الأصناف', index=False)
            
        return output_path
        
    def _create_items_csv_report(self, data, output_path):
        """إنشاء تقرير CSV للأصناف"""
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"items_report_{timestamp}.csv"
            
        df = pd.DataFrame(data)
        df.to_csv(output_path, index=False, encoding='utf-8-sig')
        return output_path
        
    def _create_stock_pdf_report(self, data, output_path):
        """إنشاء تقرير PDF للمخزون"""
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"stock_report_{timestamp}.pdf"
            
        return self._create_generic_pdf_report(data, output_path, "تقرير المخزون")
        
    def _create_stock_excel_report(self, data, output_path):
        """إنشاء تقرير Excel للمخزون"""
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"stock_report_{timestamp}.xlsx"
            
        df = pd.DataFrame(data)
        
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='تقرير المخزون', index=False)
            
        return output_path
        
    def _create_stock_csv_report(self, data, output_path):
        """إنشاء تقرير CSV للمخزون"""
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"stock_report_{timestamp}.csv"
            
        df = pd.DataFrame(data)
        df.to_csv(output_path, index=False, encoding='utf-8-sig')
        return output_path
        
    def _create_generic_pdf_report(self, data, output_path, title):
        """إنشاء تقرير PDF عام"""
        doc = SimpleDocTemplate(output_path, pagesize=A4)
        story = []
        
        # العنوان
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=1
        )
        
        title_para = Paragraph(title, title_style)
        story.append(title_para)
        story.append(Spacer(1, 12))
        
        # إضافة تاريخ التقرير
        date_style = ParagraphStyle(
            'DateStyle',
            parent=styles['Normal'],
            fontSize=12,
            alignment=1
        )
        
        date_para = Paragraph(f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}", date_style)
        story.append(date_para)
        story.append(Spacer(1, 20))
        
        # إنشاء الجدول
        if data:
            # استخدام أول عنصر لتحديد الأعمدة
            headers = list(data[0].keys())
            table_data = [headers]
            
            for item in data:
                row = [str(item.get(header, '')) for header in headers]
                table_data.append(row)
                
            table = Table(table_data)
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(table)
        else:
            story.append(Paragraph("لا توجد بيانات للعرض", styles['Normal']))
            
        doc.build(story)
        return output_path
