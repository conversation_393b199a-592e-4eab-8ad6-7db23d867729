#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة إدارة المعدات
Equipment Management Module for Supply Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime

class EquipmentModule:
    """وحدة إدارة المعدات"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        
        # إنشاء الإطار الرئيسي
        self.frame = ttk_bs.Frame(parent)
        
        # متغيرات النموذج
        self.setup_variables()
        
        # إعداد واجهة المستخدم
        self.setup_ui()
        
        # تحميل البيانات
        self.load_data()
        
    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        self.equipment_name_var = tk.StringVar()
        self.equipment_code_var = tk.StringVar()
        self.category_var = tk.StringVar()
        self.model_var = tk.StringVar()
        self.manufacturer_var = tk.StringVar()
        self.serial_number_var = tk.StringVar()
        self.status_var = tk.StringVar()
        self.unit_var = tk.StringVar()
        self.location_var = tk.StringVar()
        self.notes_var = tk.StringVar()
        self.selected_equipment_id = None
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # العنوان الرئيسي
        title_frame = ttk_bs.Frame(self.frame)
        title_frame.pack(fill=X, padx=10, pady=10)
        
        title_label = ttk_bs.Label(
            title_frame,
            text="⚙️ إدارة المعدات والأجهزة",
            font=("Arial", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack(side=LEFT)
        
        # أزرار سريعة
        quick_buttons_frame = ttk_bs.Frame(title_frame)
        quick_buttons_frame.pack(side=RIGHT)
        
        export_btn = ttk_bs.Button(
            quick_buttons_frame,
            text="📊 تصدير",
            bootstyle="info-outline",
            command=self.export_equipment
        )
        export_btn.pack(side=LEFT, padx=5)
        
        import_btn = ttk_bs.Button(
            quick_buttons_frame,
            text="📥 استيراد",
            bootstyle="success-outline",
            command=self.import_equipment
        )
        import_btn.pack(side=LEFT, padx=5)
        
        # تقسيم الشاشة
        main_paned = ttk_bs.PanedWindow(self.frame, orient=HORIZONTAL)
        main_paned.pack(fill=BOTH, expand=True, padx=10, pady=5)
        
        # الجانب الأيسر - نموذج الإدخال
        left_frame = ttk_bs.LabelFrame(main_paned, text="بيانات المعدة", padding=10)
        main_paned.add(left_frame, weight=1)
        
        self.create_equipment_form(left_frame)
        
        # الجانب الأيمن - قائمة المعدات
        right_frame = ttk_bs.LabelFrame(main_paned, text="قائمة المعدات", padding=10)
        main_paned.add(right_frame, weight=2)
        
        self.create_equipment_list(right_frame)
        
    def create_equipment_form(self, parent):
        """إنشاء نموذج بيانات المعدة"""
        
        # اسم المعدة
        ttk_bs.Label(parent, text="اسم المعدة:").grid(row=0, column=0, sticky=W, pady=5)
        equipment_name_entry = ttk_bs.Entry(parent, textvariable=self.equipment_name_var, width=30)
        equipment_name_entry.grid(row=0, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # رمز المعدة
        ttk_bs.Label(parent, text="رمز المعدة:").grid(row=1, column=0, sticky=W, pady=5)
        equipment_code_entry = ttk_bs.Entry(parent, textvariable=self.equipment_code_var, width=30)
        equipment_code_entry.grid(row=1, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # فئة المعدة
        ttk_bs.Label(parent, text="الفئة:").grid(row=2, column=0, sticky=W, pady=5)
        category_combo = ttk_bs.Combobox(
            parent,
            textvariable=self.category_var,
            values=["أسلحة", "مركبات", "اتصالات", "طبية", "هندسية", "إلكترونية", "أخرى"],
            width=27,
            state="readonly"
        )
        category_combo.grid(row=2, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # الطراز
        ttk_bs.Label(parent, text="الطراز:").grid(row=3, column=0, sticky=W, pady=5)
        model_entry = ttk_bs.Entry(parent, textvariable=self.model_var, width=30)
        model_entry.grid(row=3, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # الشركة المصنعة
        ttk_bs.Label(parent, text="الشركة المصنعة:").grid(row=4, column=0, sticky=W, pady=5)
        manufacturer_entry = ttk_bs.Entry(parent, textvariable=self.manufacturer_var, width=30)
        manufacturer_entry.grid(row=4, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # الرقم التسلسلي
        ttk_bs.Label(parent, text="الرقم التسلسلي:").grid(row=5, column=0, sticky=W, pady=5)
        serial_entry = ttk_bs.Entry(parent, textvariable=self.serial_number_var, width=30)
        serial_entry.grid(row=5, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # الحالة
        ttk_bs.Label(parent, text="الحالة:").grid(row=6, column=0, sticky=W, pady=5)
        status_combo = ttk_bs.Combobox(
            parent,
            textvariable=self.status_var,
            values=["نشط", "خارج الخدمة", "قيد الصيانة", "مفقود", "تالف"],
            width=27,
            state="readonly"
        )
        status_combo.grid(row=6, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # الوحدة المسؤولة
        ttk_bs.Label(parent, text="الوحدة المسؤولة:").grid(row=7, column=0, sticky=W, pady=5)
        self.unit_combo = ttk_bs.Combobox(
            parent,
            textvariable=self.unit_var,
            width=27,
            state="readonly"
        )
        self.unit_combo.grid(row=7, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # الموقع
        ttk_bs.Label(parent, text="الموقع:").grid(row=8, column=0, sticky=W, pady=5)
        location_entry = ttk_bs.Entry(parent, textvariable=self.location_var, width=30)
        location_entry.grid(row=8, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # ملاحظات
        ttk_bs.Label(parent, text="ملاحظات:").grid(row=9, column=0, sticky=W+N, pady=5)
        self.notes_text = tk.Text(parent, width=30, height=3)
        self.notes_text.grid(row=9, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # أزرار العمليات
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.grid(row=10, column=0, columnspan=2, pady=20)
        
        # زر إضافة
        add_btn = ttk_bs.Button(
            buttons_frame,
            text="➕ إضافة",
            bootstyle="success",
            command=self.add_equipment
        )
        add_btn.pack(side=LEFT, padx=5)
        
        # زر تحديث
        update_btn = ttk_bs.Button(
            buttons_frame,
            text="✏️ تحديث",
            bootstyle="warning",
            command=self.update_equipment
        )
        update_btn.pack(side=LEFT, padx=5)
        
        # زر حذف
        delete_btn = ttk_bs.Button(
            buttons_frame,
            text="🗑️ حذف",
            bootstyle="danger",
            command=self.delete_equipment
        )
        delete_btn.pack(side=LEFT, padx=5)
        
        # زر مسح النموذج
        clear_btn = ttk_bs.Button(
            buttons_frame,
            text="🧹 مسح",
            bootstyle="secondary",
            command=self.clear_form
        )
        clear_btn.pack(side=LEFT, padx=5)
        
        # تكوين الأعمدة
        parent.grid_columnconfigure(1, weight=1)
        
    def create_equipment_list(self, parent):
        """إنشاء قائمة المعدات"""
        
        # إطار البحث والفلترة
        search_frame = ttk_bs.Frame(parent)
        search_frame.pack(fill=X, pady=(0, 10))
        
        # البحث
        ttk_bs.Label(search_frame, text="البحث:").pack(side=LEFT)
        self.search_var = tk.StringVar()
        search_entry = ttk_bs.Entry(search_frame, textvariable=self.search_var, width=20)
        search_entry.pack(side=LEFT, padx=(10, 5))
        search_entry.bind('<KeyRelease>', self.search_equipment)
        
        # فلتر الفئة
        ttk_bs.Label(search_frame, text="الفئة:").pack(side=LEFT, padx=(20, 5))
        self.filter_category_var = tk.StringVar()
        filter_combo = ttk_bs.Combobox(
            search_frame,
            textvariable=self.filter_category_var,
            values=["الكل", "أسلحة", "مركبات", "اتصالات", "طبية", "هندسية", "إلكترونية", "أخرى"],
            width=15,
            state="readonly"
        )
        filter_combo.pack(side=LEFT, padx=5)
        filter_combo.bind('<<ComboboxSelected>>', self.filter_equipment)
        filter_combo.set("الكل")
        
        # جدول المعدات
        columns = ("الرقم", "اسم المعدة", "الرمز", "الفئة", "الطراز", "الحالة", "الوحدة")
        self.equipment_tree = ttk.Treeview(
            parent,
            columns=columns,
            show="headings",
            height=15
        )
        
        # تكوين الأعمدة
        column_widths = [60, 200, 100, 100, 120, 100, 150]
        for i, (col, width) in enumerate(zip(columns, column_widths)):
            self.equipment_tree.heading(col, text=col)
            self.equipment_tree.column(col, width=width, minwidth=50)
        
        # شريط التمرير
        equipment_scrollbar = ttk.Scrollbar(
            parent,
            orient=VERTICAL,
            command=self.equipment_tree.yview
        )
        self.equipment_tree.configure(yscrollcommand=equipment_scrollbar.set)
        
        # ربط الأحداث
        self.equipment_tree.bind('<Double-1>', self.on_equipment_select)
        
        # تخطيط العناصر
        self.equipment_tree.pack(side=LEFT, fill=BOTH, expand=True)
        equipment_scrollbar.pack(side=RIGHT, fill=Y)
        
    def add_equipment(self):
        """إضافة معدة جديدة"""
        if not self.validate_equipment_form():
            return
            
        try:
            query = """
            INSERT INTO equipment (equipment_name, equipment_code, category, model, manufacturer, 
                                 serial_number, status, unit_id, location, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            # الحصول على معرف الوحدة
            unit_id = self.get_unit_id_by_name(self.unit_var.get())
            
            params = (
                self.equipment_name_var.get(),
                self.equipment_code_var.get(),
                self.category_var.get(),
                self.model_var.get(),
                self.manufacturer_var.get(),
                self.serial_number_var.get(),
                self.status_var.get(),
                unit_id,
                self.location_var.get(),
                self.notes_text.get("1.0", tk.END).strip()
            )
            
            self.db_manager.execute_query(query, params)
            
            messagebox.showinfo("نجح", "تم إضافة المعدة بنجاح")
            self.clear_form()
            self.load_equipment()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إضافة المعدة: {str(e)}")
            
    def update_equipment(self):
        """تحديث بيانات المعدة"""
        if not self.selected_equipment_id:
            messagebox.showwarning("تحذير", "يرجى اختيار معدة للتحديث")
            return
            
        if not self.validate_equipment_form():
            return
            
        try:
            query = """
            UPDATE equipment 
            SET equipment_name=?, equipment_code=?, category=?, model=?, manufacturer=?, 
                serial_number=?, status=?, unit_id=?, location=?, notes=?, updated_at=?
            WHERE id=?
            """
            
            # الحصول على معرف الوحدة
            unit_id = self.get_unit_id_by_name(self.unit_var.get())
            
            params = (
                self.equipment_name_var.get(),
                self.equipment_code_var.get(),
                self.category_var.get(),
                self.model_var.get(),
                self.manufacturer_var.get(),
                self.serial_number_var.get(),
                self.status_var.get(),
                unit_id,
                self.location_var.get(),
                self.notes_text.get("1.0", tk.END).strip(),
                datetime.now().isoformat(),
                self.selected_equipment_id
            )
            
            self.db_manager.execute_query(query, params)
            
            messagebox.showinfo("نجح", "تم تحديث بيانات المعدة بنجاح")
            self.clear_form()
            self.load_equipment()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحديث المعدة: {str(e)}")
            
    def delete_equipment(self):
        """حذف المعدة"""
        if not self.selected_equipment_id:
            messagebox.showwarning("تحذير", "يرجى اختيار معدة للحذف")
            return
            
        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذه المعدة؟"):
            try:
                query = "DELETE FROM equipment WHERE id=?"
                self.db_manager.execute_query(query, (self.selected_equipment_id,))
                
                messagebox.showinfo("نجح", "تم حذف المعدة بنجاح")
                self.clear_form()
                self.load_equipment()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في حذف المعدة: {str(e)}")
                
    def validate_equipment_form(self):
        """التحقق من صحة بيانات النموذج"""
        if not self.equipment_name_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم المعدة")
            return False
            
        if not self.equipment_code_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال رمز المعدة")
            return False
            
        if not self.category_var.get().strip():
            messagebox.showerror("خطأ", "يرجى اختيار فئة المعدة")
            return False
            
        return True

    def clear_form(self):
        """مسح النموذج"""
        self.equipment_name_var.set("")
        self.equipment_code_var.set("")
        self.category_var.set("")
        self.model_var.set("")
        self.manufacturer_var.set("")
        self.serial_number_var.set("")
        self.status_var.set("")
        self.unit_var.set("")
        self.location_var.set("")
        self.notes_text.delete("1.0", tk.END)
        self.selected_equipment_id = None

    def load_equipment(self):
        """تحميل قائمة المعدات"""
        # مسح البيانات الحالية
        for item in self.equipment_tree.get_children():
            self.equipment_tree.delete(item)

        # جلب البيانات من قاعدة البيانات
        equipment_list = self.db_manager.fetch_all("""
            SELECT e.*, u.unit_name
            FROM equipment e
            LEFT JOIN military_units u ON e.unit_id = u.id
            ORDER BY e.equipment_name
        """)

        # إضافة البيانات إلى الجدول
        for equipment in equipment_list:
            self.equipment_tree.insert("", "end", values=(
                equipment['id'],
                equipment['equipment_name'],
                equipment['equipment_code'],
                equipment['category'],
                equipment['model'] or "",
                equipment['status'],
                equipment['unit_name'] or ""
            ))

    def search_equipment(self, event=None):
        """البحث في المعدات"""
        search_term = self.search_var.get().strip()
        category_filter = self.filter_category_var.get()

        # بناء الاستعلام
        query = """
            SELECT e.*, u.unit_name
            FROM equipment e
            LEFT JOIN military_units u ON e.unit_id = u.id
            WHERE 1=1
        """
        params = []

        if search_term:
            query += " AND (e.equipment_name LIKE ? OR e.equipment_code LIKE ? OR e.model LIKE ?)"
            search_pattern = f"%{search_term}%"
            params.extend([search_pattern, search_pattern, search_pattern])

        if category_filter and category_filter != "الكل":
            query += " AND e.category = ?"
            params.append(category_filter)

        query += " ORDER BY e.equipment_name"

        # مسح البيانات الحالية
        for item in self.equipment_tree.get_children():
            self.equipment_tree.delete(item)

        # جلب النتائج
        equipment_list = self.db_manager.fetch_all(query, params)

        # إضافة النتائج إلى الجدول
        for equipment in equipment_list:
            self.equipment_tree.insert("", "end", values=(
                equipment['id'],
                equipment['equipment_name'],
                equipment['equipment_code'],
                equipment['category'],
                equipment['model'] or "",
                equipment['status'],
                equipment['unit_name'] or ""
            ))

    def filter_equipment(self, event=None):
        """فلترة المعدات حسب الفئة"""
        self.search_equipment()

    def on_equipment_select(self, event):
        """عند اختيار معدة من القائمة"""
        selection = self.equipment_tree.selection()
        if selection:
            item = self.equipment_tree.item(selection[0])
            equipment_id = item['values'][0]

            # جلب بيانات المعدة
            equipment = self.db_manager.fetch_one("SELECT * FROM equipment WHERE id=?", (equipment_id,))

            if equipment:
                # ملء النموذج بالبيانات
                self.equipment_name_var.set(equipment['equipment_name'])
                self.equipment_code_var.set(equipment['equipment_code'])
                self.category_var.set(equipment['category'])
                self.model_var.set(equipment['model'] or "")
                self.manufacturer_var.set(equipment['manufacturer'] or "")
                self.serial_number_var.set(equipment['serial_number'] or "")
                self.status_var.set(equipment['status'])
                self.location_var.set(equipment['location'] or "")

                # ملء الوحدة
                if equipment['unit_id']:
                    unit = self.db_manager.fetch_one("SELECT unit_name FROM military_units WHERE id=?", (equipment['unit_id'],))
                    if unit:
                        self.unit_var.set(unit['unit_name'])

                # ملء الملاحظات
                self.notes_text.delete("1.0", tk.END)
                if equipment['notes']:
                    self.notes_text.insert("1.0", equipment['notes'])

                self.selected_equipment_id = equipment['id']

    def get_unit_id_by_name(self, unit_name):
        """الحصول على معرف الوحدة بالاسم"""
        if not unit_name:
            return None

        unit = self.db_manager.fetch_one("SELECT id FROM military_units WHERE unit_name=?", (unit_name,))
        return unit['id'] if unit else None

    def load_units(self):
        """تحميل قائمة الوحدات العسكرية"""
        units = self.db_manager.fetch_all("SELECT unit_name FROM military_units ORDER BY unit_name")
        unit_names = [unit['unit_name'] for unit in units]
        self.unit_combo['values'] = unit_names

    def export_equipment(self):
        """تصدير بيانات المعدات"""
        try:
            import pandas as pd
            from tkinter import filedialog

            # جلب البيانات
            equipment_list = self.db_manager.fetch_all("""
                SELECT e.equipment_name as 'اسم المعدة',
                       e.equipment_code as 'رمز المعدة',
                       e.category as 'الفئة',
                       e.model as 'الطراز',
                       e.manufacturer as 'الشركة المصنعة',
                       e.serial_number as 'الرقم التسلسلي',
                       e.status as 'الحالة',
                       u.unit_name as 'الوحدة',
                       e.location as 'الموقع'
                FROM equipment e
                LEFT JOIN military_units u ON e.unit_id = u.id
                ORDER BY e.equipment_name
            """)

            if not equipment_list:
                messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")
                return

            # اختيار مكان الحفظ
            file_path = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("CSV files", "*.csv")]
            )

            if file_path:
                df = pd.DataFrame(equipment_list)

                if file_path.endswith('.xlsx'):
                    df.to_excel(file_path, index=False)
                else:
                    df.to_csv(file_path, index=False, encoding='utf-8-sig')

                messagebox.showinfo("نجح", f"تم تصدير البيانات إلى: {file_path}")

        except ImportError:
            messagebox.showerror("خطأ", "مكتبة pandas غير مثبتة")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في التصدير: {str(e)}")

    def import_equipment(self):
        """استيراد بيانات المعدات"""
        messagebox.showinfo("قريباً", "ميزة الاستيراد ستكون متاحة قريباً")

    def load_data(self):
        """تحميل البيانات الأولية"""
        self.load_units()
        self.load_equipment()
