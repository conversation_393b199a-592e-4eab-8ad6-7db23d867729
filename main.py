#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة التموين العام والقوة العمومية
Supply Management System for Public Forces

المطور: نظام شامل لإدارة التموين
التاريخ: 2025
"""

import tkinter as tk
from tkinter import messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime

# استيراد الوحدات
from database.db_manager import DatabaseManager
from modules.dashboard import Dashboard
from modules.basic_info import BasicInfoModule
from modules.equipment import EquipmentModule
from modules.items import ItemsModule
from modules.users import UserManagement
from modules.backup import BackupModule
from modules.items import ItemsModule
from modules.users import UserManagement
from modules.backup import BackupModule
from utils.language_manager import LanguageManager
from utils.reports import ReportsManager

class SupplyManagementSystem:
    """الفئة الرئيسية لنظام إدارة التموين"""
    
    def __init__(self):
        # إعداد النافذة الرئيسية
        self.root = ttk_bs.Window(themename="cosmo")
        self.root.title("نظام إدارة التموين العام والقوة العمومية")
        self.root.geometry("1400x900")
        self.root.state('zoomed')  # ملء الشاشة
        
        # إعداد المتغيرات
        self.current_user = None
        self.current_language = "ar"
        
        # إنشاء مدير قاعدة البيانات
        self.db_manager = DatabaseManager()
        
        # إنشاء مدير اللغات
        self.lang_manager = LanguageManager()
        
        # إنشاء مدير التقارير
        self.reports_manager = ReportsManager(self.db_manager)
        
        # إعداد الواجهة
        self.setup_ui()
        
        # تحميل البيانات الأولية
        self.load_initial_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إنشاء شريط القوائم
        self.create_menu_bar()
        
        # إنشاء شريط الأدوات
        self.create_toolbar()
        
        # إنشاء المنطقة الرئيسية
        self.create_main_area()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
        
    def create_menu_bar(self):
        """إنشاء شريط القوائم العلوية"""
        self.menubar = tk.Menu(self.root)
        self.root.config(menu=self.menubar)
        
        # قائمة ملف
        file_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="جديد", command=self.new_file)
        file_menu.add_command(label="فتح", command=self.open_file)
        file_menu.add_command(label="حفظ", command=self.save_file)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.exit_app)
        
        # قائمة جهات الإضافة
        entities_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="🏷️ جهات الإضافة", menu=entities_menu)
        entities_menu.add_command(label="إضافة جهة جديدة", command=self.add_entity)
        entities_menu.add_command(label="إدارة الجهات", command=self.manage_entities)
        
        # قائمة الإعدادات
        settings_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="🛠️ الإعدادات", menu=settings_menu)
        settings_menu.add_command(label="إعدادات النظام", command=self.system_settings)
        settings_menu.add_command(label="إعدادات المستخدم", command=self.user_settings)
        
        # قائمة التقارير
        reports_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="📑 التقارير", menu=reports_menu)
        reports_menu.add_command(label="تقرير المعدات", command=self.equipment_report)
        reports_menu.add_command(label="تقرير الأصناف", command=self.items_report)
        reports_menu.add_command(label="تقرير شامل", command=self.comprehensive_report)
        
        # قائمة اللغات
        language_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="🌍 اللغة", menu=language_menu)
        language_menu.add_command(label="العربية", command=lambda: self.change_language("ar"))
        language_menu.add_command(label="English", command=lambda: self.change_language("en"))
        
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        self.toolbar_frame = ttk_bs.Frame(self.root)
        self.toolbar_frame.pack(fill=X, padx=5, pady=5)
        
        # أزرار الوحدات الرئيسية
        buttons_config = [
            ("🏠 لوحة التحكم", "primary", self.show_dashboard),
            ("🧩 المعلومات الأساسية", "info", self.show_basic_info),
            ("⚙️ المعدات", "warning", self.show_equipment),
            ("📦 الأصناف", "success", self.show_items),
            ("🔐 إدارة المستخدمين", "danger", self.show_users),
            ("💾 النسخ الاحتياطي", "secondary", self.show_backup)
        ]
        
        for text, style, command in buttons_config:
            btn = ttk_bs.Button(
                self.toolbar_frame,
                text=text,
                bootstyle=style,
                command=command,
                width=20
            )
            btn.pack(side=LEFT, padx=2)
            
    def create_main_area(self):
        """إنشاء المنطقة الرئيسية"""
        # إطار رئيسي
        self.main_frame = ttk_bs.Frame(self.root)
        self.main_frame.pack(fill=BOTH, expand=True, padx=5, pady=5)
        
        # إنشاء دفتر الملاحظات للوحدات
        self.notebook = ttk_bs.Notebook(self.main_frame)
        self.notebook.pack(fill=BOTH, expand=True)
        
        # إنشاء الوحدات
        self.create_modules()
        
    def create_modules(self):
        """إنشاء وحدات النظام"""
        # لوحة التحكم
        self.dashboard = Dashboard(self.notebook, self.db_manager)
        self.notebook.add(self.dashboard.frame, text="🏠 لوحة التحكم")
        
        # وحدة المعلومات الأساسية
        self.basic_info = BasicInfoModule(self.notebook, self.db_manager)
        self.notebook.add(self.basic_info.frame, text="🧩 المعلومات الأساسية")
        
        # وحدة المعدات
        self.equipment = EquipmentModule(self.notebook, self.db_manager)
        self.notebook.add(self.equipment.frame, text="⚙️ المعدات")
        
        # وحدة الأصناف
        self.items = ItemsModule(self.notebook, self.db_manager)
        self.notebook.add(self.items.frame, text="📦 الأصناف")
        
        # وحدة إدارة المستخدمين
        self.users = UserManagement(self.notebook, self.db_manager)
        self.notebook.add(self.users.frame, text="🔐 إدارة المستخدمين")
        
        # وحدة النسخ الاحتياطي
        self.backup = BackupModule(self.notebook, self.db_manager)
        self.notebook.add(self.backup.frame, text="💾 النسخ الاحتياطي")
        
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_frame = ttk_bs.Frame(self.root)
        self.status_frame.pack(fill=X, side=BOTTOM)
        
        # معلومات المستخدم الحالي
        self.user_label = ttk_bs.Label(
            self.status_frame,
            text="المستخدم: غير مسجل الدخول",
            bootstyle="info"
        )
        self.user_label.pack(side=LEFT, padx=5)
        
        # الوقت والتاريخ
        self.time_label = ttk_bs.Label(
            self.status_frame,
            text="",
            bootstyle="secondary"
        )
        self.time_label.pack(side=RIGHT, padx=5)
        
        # تحديث الوقت
        self.update_time()
        
    def update_time(self):
        """تحديث الوقت في شريط الحالة"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)
        
    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        try:
            # إنشاء قاعدة البيانات والجداول
            self.db_manager.create_tables()
            
            # تحميل إعدادات اللغة
            self.lang_manager.load_language(self.current_language)
            
            messagebox.showinfo("نجح", "تم تحميل النظام بنجاح")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل النظام: {str(e)}")
            
    # وظائف شريط القوائم
    def new_file(self):
        """إنشاء ملف جديد"""
        pass
        
    def open_file(self):
        """فتح ملف"""
        pass
        
    def save_file(self):
        """حفظ ملف"""
        pass
        
    def exit_app(self):
        """إغلاق التطبيق"""
        if messagebox.askokcancel("تأكيد الخروج", "هل تريد إغلاق التطبيق؟"):
            self.root.quit()
            
    def add_entity(self):
        """إضافة جهة جديدة"""
        pass
        
    def manage_entities(self):
        """إدارة الجهات"""
        pass
        
    def system_settings(self):
        """إعدادات النظام"""
        pass
        
    def user_settings(self):
        """إعدادات المستخدم"""
        pass
        
    def equipment_report(self):
        """تقرير المعدات"""
        self.reports_manager.generate_equipment_report()
        
    def items_report(self):
        """تقرير الأصناف"""
        self.reports_manager.generate_items_report()
        
    def comprehensive_report(self):
        """تقرير شامل"""
        self.reports_manager.generate_comprehensive_report()
        
    def change_language(self, lang):
        """تغيير اللغة"""
        self.current_language = lang
        self.lang_manager.load_language(lang)
        # تحديث واجهة المستخدم
        self.update_ui_language()
        
    def update_ui_language(self):
        """تحديث لغة واجهة المستخدم"""
        pass
        
    # وظائف أزرار شريط الأدوات
    def show_dashboard(self):
        """عرض لوحة التحكم"""
        self.notebook.select(0)
        
    def show_basic_info(self):
        """عرض وحدة المعلومات الأساسية"""
        self.notebook.select(1)
        
    def show_equipment(self):
        """عرض وحدة المعدات"""
        self.notebook.select(2)
        
    def show_items(self):
        """عرض وحدة الأصناف"""
        self.notebook.select(3)
        
    def show_users(self):
        """عرض وحدة إدارة المستخدمين"""
        self.notebook.select(4)
        
    def show_backup(self):
        """عرض وحدة النسخ الاحتياطي"""
        self.notebook.select(5)
        
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    # إنشاء وتشغيل التطبيق
    app = SupplyManagementSystem()
    app.run()
