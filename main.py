#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة التموين العام والقوة العمومية
Supply Management System for Public Forces

المطور: نظام شامل لإدارة التموين
التاريخ: 2025
"""

import tkinter as tk
from tkinter import messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime
import hashlib

# استيراد الوحدات
from database.db_manager import DatabaseManager
from modules.dashboard import Dashboard
from modules.basic_info import BasicInfoModule
from modules.equipment import EquipmentModule
from modules.items import ItemsModule
from modules.users import UserManagement
from modules.backup import BackupModule
from utils.language_manager import LanguageManager
from utils.reports import ReportsManager

class LoginWindow:
    """نافذة تسجيل الدخول"""

    def __init__(self):
        self.root = ttk_bs.Window(themename="cosmo")
        self.root.title("تسجيل الدخول - نظام إدارة التموين")
        self.root.geometry("400x300")
        self.root.resizable(False, False)

        # توسيط النافذة
        self.center_window()

        # متغيرات تسجيل الدخول
        self.username_var = tk.StringVar()
        self.password_var = tk.StringVar()
        self.logged_in_user = None

        # إنشاء مدير قاعدة البيانات
        self.db_manager = DatabaseManager()

        # إنشاء واجهة تسجيل الدخول
        self.create_login_interface()

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def create_login_interface(self):
        """إنشاء واجهة تسجيل الدخول"""
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.root, padding=20)
        main_frame.pack(fill=BOTH, expand=True)

        # العنوان
        title_label = ttk_bs.Label(
            main_frame,
            text="نظام إدارة التموين العام والقوة العمومية",
            font=("Arial", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack(pady=(0, 20))

        # عنوان فرعي
        subtitle_label = ttk_bs.Label(
            main_frame,
            text="تسجيل الدخول",
            font=("Arial", 14),
            bootstyle="secondary"
        )
        subtitle_label.pack(pady=(0, 30))

        # إطار النموذج
        form_frame = ttk_bs.Frame(main_frame)
        form_frame.pack(fill=X, pady=10)

        # اسم المستخدم
        username_label = ttk_bs.Label(form_frame, text="اسم المستخدم:")
        username_label.pack(anchor=W, pady=(0, 5))

        self.username_entry = ttk_bs.Entry(
            form_frame,
            textvariable=self.username_var,
            font=("Arial", 12),
            width=30
        )
        self.username_entry.pack(fill=X, pady=(0, 15))

        # كلمة المرور
        password_label = ttk_bs.Label(form_frame, text="كلمة المرور:")
        password_label.pack(anchor=W, pady=(0, 5))

        self.password_entry = ttk_bs.Entry(
            form_frame,
            textvariable=self.password_var,
            show="*",
            font=("Arial", 12),
            width=30
        )
        self.password_entry.pack(fill=X, pady=(0, 20))

        # أزرار
        buttons_frame = ttk_bs.Frame(form_frame)
        buttons_frame.pack(fill=X, pady=10)

        login_button = ttk_bs.Button(
            buttons_frame,
            text="تسجيل الدخول",
            command=self.login,
            bootstyle="success",
            width=15
        )
        login_button.pack(side=LEFT, padx=(0, 10))

        exit_button = ttk_bs.Button(
            buttons_frame,
            text="خروج",
            command=self.root.quit,
            bootstyle="danger",
            width=15
        )
        exit_button.pack(side=LEFT)

        # ربط مفتاح Enter بتسجيل الدخول
        self.root.bind('<Return>', lambda event: self.login())

        # تركيز على حقل اسم المستخدم
        self.username_entry.focus()

        # معلومات المستخدم الافتراضي
        info_frame = ttk_bs.Frame(main_frame)
        info_frame.pack(fill=X, pady=(20, 0))

        info_label = ttk_bs.Label(
            info_frame,
            text="المستخدم الافتراضي: admin | كلمة المرور: admin123",
            font=("Arial", 10),
            bootstyle="info"
        )
        info_label.pack()

    def hash_password(self, password):
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()

    def login(self):
        """تسجيل الدخول"""
        username = self.username_var.get().strip()
        password = self.password_var.get().strip()

        if not username or not password:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return

        try:
            # البحث عن المستخدم
            query = "SELECT * FROM users WHERE username = ? AND is_active = 1"
            user = self.db_manager.fetch_one(query, (username,))

            if not user:
                messagebox.showerror("خطأ", "اسم المستخدم غير صحيح أو الحساب غير مفعل")
                return

            # التحقق من كلمة المرور
            hashed_password = self.hash_password(password)
            if user['password_hash'] != hashed_password:
                messagebox.showerror("خطأ", "كلمة المرور غير صحيحة")
                return

            # حفظ بيانات المستخدم
            self.logged_in_user = user

            # تسجيل نشاط تسجيل الدخول
            self.db_manager.log_activity(
                user['id'],
                "تسجيل دخول",
                "users",
                user['id']
            )

            # رسالة نجاح تسجيل الدخول
            messagebox.showinfo("نجح", f"مرحباً {user.get('full_name', user['username'])}!")

            # إغلاق نافذة تسجيل الدخول
            self.root.quit()  # استخدام quit بدلاً من destroy

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تسجيل الدخول: {str(e)}")

    def run(self):
        """تشغيل نافذة تسجيل الدخول"""
        self.root.mainloop()
        return self.logged_in_user

class SupplyManagementSystem:
    """الفئة الرئيسية لنظام إدارة التموين"""

    def __init__(self, logged_in_user=None):
        # حفظ بيانات المستخدم المسجل
        self.logged_in_user = logged_in_user

        # إعداد النافذة الرئيسية
        self.root = ttk_bs.Window(themename="cosmo")
        self.root.title("نظام إدارة التموين العام والقوة العمومية")
        self.root.geometry("1400x900")
        self.root.state('zoomed')  # ملء الشاشة
        
        # إعداد المتغيرات
        self.current_user = None
        self.current_language = "ar"
        
        # إنشاء مدير قاعدة البيانات
        self.db_manager = DatabaseManager()
        
        # إنشاء مدير اللغات
        self.lang_manager = LanguageManager()
        
        # إنشاء مدير التقارير
        self.reports_manager = ReportsManager(self.db_manager)
        
        # إعداد الواجهة
        self.setup_ui()
        
        # تحميل البيانات الأولية
        self.load_initial_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إنشاء شريط القوائم
        self.create_menu_bar()
        
        # إنشاء شريط الأدوات
        self.create_toolbar()
        
        # إنشاء المنطقة الرئيسية
        self.create_main_area()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
        
    def create_menu_bar(self):
        """إنشاء شريط القوائم العلوية"""
        self.menubar = tk.Menu(self.root)
        self.root.config(menu=self.menubar)
        
        # قائمة ملف
        file_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="جديد", command=self.new_file)
        file_menu.add_command(label="فتح", command=self.open_file)
        file_menu.add_command(label="حفظ", command=self.save_file)
        file_menu.add_separator()
        file_menu.add_command(label="تسجيل الخروج", command=self.logout)
        file_menu.add_command(label="خروج", command=self.exit_app)
        
        # قائمة جهات الإضافة
        entities_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="🏷️ جهات الإضافة", menu=entities_menu)
        entities_menu.add_command(label="إضافة جهة جديدة", command=self.add_entity)
        entities_menu.add_command(label="إدارة الجهات", command=self.manage_entities)
        
        # قائمة الإعدادات
        settings_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="🛠️ الإعدادات", menu=settings_menu)
        settings_menu.add_command(label="إعدادات النظام", command=self.system_settings)
        settings_menu.add_command(label="إعدادات المستخدم", command=self.user_settings)
        
        # قائمة التقارير
        reports_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="📑 التقارير", menu=reports_menu)
        reports_menu.add_command(label="تقرير المعدات", command=self.equipment_report)
        reports_menu.add_command(label="تقرير الأصناف", command=self.items_report)
        reports_menu.add_command(label="تقرير شامل", command=self.comprehensive_report)
        
        # قائمة اللغات
        language_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="🌍 اللغة", menu=language_menu)
        language_menu.add_command(label="العربية", command=lambda: self.change_language("ar"))
        language_menu.add_command(label="English", command=lambda: self.change_language("en"))
        
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        self.toolbar_frame = ttk_bs.Frame(self.root)
        self.toolbar_frame.pack(fill=X, padx=5, pady=5)
        
        # أزرار الوحدات الرئيسية
        buttons_config = [
            ("🏠 لوحة التحكم", "primary", self.show_dashboard),
            ("🧩 المعلومات الأساسية", "info", self.show_basic_info),
            ("⚙️ المعدات", "warning", self.show_equipment),
            ("📦 الأصناف", "success", self.show_items),
            ("🔐 إدارة المستخدمين", "danger", self.show_users),
            ("💾 النسخ الاحتياطي", "secondary", self.show_backup)
        ]
        
        for text, style, command in buttons_config:
            btn = ttk_bs.Button(
                self.toolbar_frame,
                text=text,
                bootstyle=style,
                command=command,
                width=20
            )
            btn.pack(side=LEFT, padx=2)
            
    def create_main_area(self):
        """إنشاء المنطقة الرئيسية"""
        # إطار رئيسي
        self.main_frame = ttk_bs.Frame(self.root)
        self.main_frame.pack(fill=BOTH, expand=True, padx=5, pady=5)
        
        # إنشاء دفتر الملاحظات للوحدات
        self.notebook = ttk_bs.Notebook(self.main_frame)
        self.notebook.pack(fill=BOTH, expand=True)
        
        # إنشاء الوحدات
        self.create_modules()

        # إنشاء شريط الحالة
        self.create_status_bar()
        
    def create_modules(self):
        """إنشاء وحدات النظام"""
        # لوحة التحكم
        self.dashboard = Dashboard(self.notebook, self.db_manager)
        self.notebook.add(self.dashboard.frame, text="🏠 لوحة التحكم")
        
        # وحدة المعلومات الأساسية
        self.basic_info = BasicInfoModule(self.notebook, self.db_manager)
        self.notebook.add(self.basic_info.frame, text="🧩 المعلومات الأساسية")
        
        # وحدة المعدات
        self.equipment = EquipmentModule(self.notebook, self.db_manager)
        self.notebook.add(self.equipment.frame, text="⚙️ المعدات")
        
        # وحدة الأصناف
        self.items = ItemsModule(self.notebook, self.db_manager)
        self.notebook.add(self.items.frame, text="📦 الأصناف")
        
        # وحدة إدارة المستخدمين
        self.users = UserManagement(self.notebook, self.db_manager)
        self.notebook.add(self.users.frame, text="🔐 إدارة المستخدمين")
        
        # وحدة النسخ الاحتياطي
        self.backup = BackupModule(self.notebook, self.db_manager)
        self.notebook.add(self.backup.frame, text="💾 النسخ الاحتياطي")

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_frame = ttk_bs.Frame(self.root)
        self.status_frame.pack(fill=X, side=BOTTOM)

        # معلومات المستخدم
        if self.logged_in_user:
            user_info = f"المستخدم: {self.logged_in_user.get('full_name', 'غير محدد')} | الدور: {self.logged_in_user.get('role', 'غير محدد')}"
        else:
            user_info = "غير مسجل الدخول"

        self.user_label = ttk_bs.Label(
            self.status_frame,
            text=user_info,
            bootstyle="info"
        )
        self.user_label.pack(side=LEFT, padx=10, pady=5)

        # الوقت الحالي
        self.time_label = ttk_bs.Label(
            self.status_frame,
            text="",
            bootstyle="secondary"
        )
        self.time_label.pack(side=RIGHT, padx=10, pady=5)

        # تحديث الوقت
        self.update_time()

    def update_time(self):
        """تحديث الوقت في شريط الحالة"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)  # تحديث كل ثانية
        
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_frame = ttk_bs.Frame(self.root)
        self.status_frame.pack(fill=X, side=BOTTOM)
        
        # معلومات المستخدم الحالي
        self.user_label = ttk_bs.Label(
            self.status_frame,
            text="المستخدم: غير مسجل الدخول",
            bootstyle="info"
        )
        self.user_label.pack(side=LEFT, padx=5)
        
        # الوقت والتاريخ
        self.time_label = ttk_bs.Label(
            self.status_frame,
            text="",
            bootstyle="secondary"
        )
        self.time_label.pack(side=RIGHT, padx=5)
        
        # تحديث الوقت
        self.update_time()
        
    def update_time(self):
        """تحديث الوقت في شريط الحالة"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)
        
    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        try:
            # إنشاء قاعدة البيانات والجداول
            self.db_manager.create_tables()
            
            # تحميل إعدادات اللغة
            self.lang_manager.load_language(self.current_language)
            
            messagebox.showinfo("نجح", "تم تحميل النظام بنجاح")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل النظام: {str(e)}")
            
    # وظائف شريط القوائم
    def new_file(self):
        """إنشاء ملف جديد"""
        pass
        
    def open_file(self):
        """فتح ملف"""
        pass
        
    def save_file(self):
        """حفظ ملف"""
        pass

    def logout(self):
        """تسجيل الخروج"""
        if messagebox.askyesno("تأكيد", "هل تريد تسجيل الخروج؟"):
            # تسجيل نشاط تسجيل الخروج
            if self.logged_in_user:
                self.db_manager.log_activity(
                    self.logged_in_user['id'],
                    "تسجيل خروج",
                    "users",
                    self.logged_in_user['id']
                )

            # إغلاق النافذة الحالية
            self.root.destroy()

            # تشغيل نافذة تسجيل الدخول مرة أخرى
            login_window = LoginWindow()
            logged_in_user = login_window.run()

            # إذا تم تسجيل الدخول بنجاح، تشغيل النظام الرئيسي
            if logged_in_user:
                app = SupplyManagementSystem(logged_in_user)
                app.run()

    def exit_app(self):
        """إغلاق التطبيق"""
        if messagebox.askokcancel("تأكيد الخروج", "هل تريد إغلاق التطبيق؟"):
            # تسجيل نشاط الخروج
            if self.logged_in_user:
                self.db_manager.log_activity(
                    self.logged_in_user['id'],
                    "خروج من النظام",
                    "users",
                    self.logged_in_user['id']
                )
            self.root.quit()
            
    def add_entity(self):
        """إضافة جهة جديدة"""
        pass
        
    def manage_entities(self):
        """إدارة الجهات"""
        pass
        
    def system_settings(self):
        """إعدادات النظام"""
        pass
        
    def user_settings(self):
        """إعدادات المستخدم"""
        pass
        
    def equipment_report(self):
        """تقرير المعدات"""
        self.reports_manager.generate_equipment_report()
        
    def items_report(self):
        """تقرير الأصناف"""
        self.reports_manager.generate_items_report()
        
    def comprehensive_report(self):
        """تقرير شامل"""
        self.reports_manager.generate_comprehensive_report()
        
    def change_language(self, lang):
        """تغيير اللغة"""
        self.current_language = lang
        self.lang_manager.load_language(lang)
        # تحديث واجهة المستخدم
        self.update_ui_language()
        
    def update_ui_language(self):
        """تحديث لغة واجهة المستخدم"""
        pass
        
    # وظائف أزرار شريط الأدوات
    def show_dashboard(self):
        """عرض لوحة التحكم"""
        self.notebook.select(0)
        
    def show_basic_info(self):
        """عرض وحدة المعلومات الأساسية"""
        self.notebook.select(1)
        
    def show_equipment(self):
        """عرض وحدة المعدات"""
        self.notebook.select(2)
        
    def show_items(self):
        """عرض وحدة الأصناف"""
        self.notebook.select(3)
        
    def show_users(self):
        """عرض وحدة إدارة المستخدمين"""
        self.notebook.select(4)
        
    def show_backup(self):
        """عرض وحدة النسخ الاحتياطي"""
        self.notebook.select(5)
        
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    # تشغيل نافذة تسجيل الدخول أولاً
    login_window = LoginWindow()
    logged_in_user = login_window.run()

    # إذا تم تسجيل الدخول بنجاح، تشغيل النظام الرئيسي
    if logged_in_user:
        app = SupplyManagementSystem(logged_in_user)
        app.run()
