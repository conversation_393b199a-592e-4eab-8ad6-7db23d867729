#!/bin/bash

# نظام إدارة التموين العام والقوة العمومية
# Supply Management System

echo "========================================"
echo "   نظام إدارة التموين العام والقوة العمومية"
echo "   Supply Management System"
echo "========================================"
echo

# التحقق من Python
echo "جاري التحقق من Python..."
if ! command -v python3 &> /dev/null; then
    echo "خطأ: Python3 غير مثبت على النظام"
    echo "يرجى تثبيت Python 3.8 أو أحدث"
    exit 1
fi

# إنشاء البيئة الافتراضية إذا لم تكن موجودة
if [ ! -d "venv" ]; then
    echo "إنشاء البيئة الافتراضية..."
    python3 -m venv venv
fi

# تفعيل البيئة الافتراضية
echo "تفعيل البيئة الافتراضية..."
source venv/bin/activate

# تثبيت المتطلبات
echo "تثبيت المتطلبات..."
pip install -r requirements.txt --quiet

# تهيئة قاعدة البيانات
echo "تهيئة قاعدة البيانات..."
python3 init_database.py

echo
echo "بدء تشغيل النظام..."
echo

# تشغيل النظام
python3 main.py

# إلغاء تفعيل البيئة الافتراضية
deactivate
