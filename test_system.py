#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام - System Test
اختبار سريع للتأكد من عمل جميع المكونات
"""

def test_imports():
    """اختبار استيراد جميع المكتبات المطلوبة"""
    try:
        print("اختبار استيراد المكتبات...")
        
        import tkinter as tk
        print("✓ tkinter")
        
        import ttkbootstrap as ttk_bs
        print("✓ ttkbootstrap")
        
        import matplotlib.pyplot as plt
        print("✓ matplotlib")
        
        import pandas as pd
        print("✓ pandas")
        
        import tkcalendar
        print("✓ tkcalendar")
        
        from reportlab.pdfgen import canvas
        print("✓ reportlab")
        
        import openpyxl
        print("✓ openpyxl")
        
        print("✅ جميع المكتبات متوفرة!")
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبة: {e}")
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    try:
        print("\nاختبار قاعدة البيانات...")
        
        from database.db_manager import DatabaseManager
        db = DatabaseManager()
        
        # اختبار الاتصال
        result = db.fetch_all("SELECT name FROM sqlite_master WHERE type='table'")
        print(f"✓ عدد الجداول: {len(result)}")
        
        # اختبار جدول المستخدمين
        users = db.fetch_all("SELECT COUNT(*) as count FROM users")
        print(f"✓ عدد المستخدمين: {users[0]['count']}")
        
        print("✅ قاعدة البيانات تعمل بشكل صحيح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def test_language_manager():
    """اختبار مدير اللغات"""
    try:
        print("\nاختبار مدير اللغات...")
        
        from utils.language_manager import LanguageManager
        lang_mgr = LanguageManager()
        
        # اختبار الحصول على النص
        text = lang_mgr.get_text("app_title")
        print(f"✓ عنوان التطبيق: {text}")
        
        # اختبار تغيير اللغة
        lang_mgr.set_language("en")
        text_en = lang_mgr.get_text("app_title")
        print(f"✓ العنوان بالإنجليزية: {text_en}")
        
        # اختبار دالة load_language
        lang_mgr.load_language("ar")
        print("✓ دالة load_language تعمل")
        
        print("✅ مدير اللغات يعمل بشكل صحيح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في مدير اللغات: {e}")
        return False

def test_reports():
    """اختبار مدير التقارير"""
    try:
        print("\nاختبار مدير التقارير...")
        
        from utils.reports import ReportsManager
        from database.db_manager import DatabaseManager
        
        db = DatabaseManager()
        reports_mgr = ReportsManager(db)
        
        print("✓ تم إنشاء مدير التقارير")
        print("✅ مدير التقارير يعمل بشكل صحيح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في مدير التقارير: {e}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("=" * 50)
    print("    اختبار نظام إدارة التموين العام")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_database,
        test_language_manager,
        test_reports
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"النتيجة النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
