# نظام إدارة التموين العام والقوة العمومية
## Supply Management System for Public Forces

نظام شامل لإدارة التموين والمعدات والأصناف للقوات العمومية، مطور باستخدام Python و Tkinter مع واجهة مستخدم عربية حديثة.

## 🌟 الميزات الرئيسية

### 📊 لوحة التحكم
- عرض إحصائيات شاملة للنظام
- رسوم بيانية تفاعلية
- مراقبة حالة المخزون
- تتبع طلبات التموين

### 📋 إدارة البيانات الأساسية
- إدارة الوحدات العسكرية
- إدارة الموردين
- تصنيف الجهات والأقسام

### ⚙️ إدارة المعدات
- تسجيل وتتبع المعدات
- إدارة حالة المعدات (نشط، صيانة، تالف)
- ربط المعدات بالوحدات المسؤولة
- تتبع الضمان وتواريخ الصيانة

### 📦 إدارة الأصناف والمخزون
- إدارة شاملة للأصناف
- تتبع مستويات المخزون
- تنبيهات المخزون المنخفض
- إدارة وحدات القياس والأسعار

### 👥 إدارة المستخدمين
- نظام مستخدمين متعدد المستويات
- إدارة الصلاحيات والأدوار
- تشفير كلمات المرور
- سجل نشاط المستخدمين

### 💾 النسخ الاحتياطي والاستعادة
- نسخ احتياطي كامل أو جزئي
- استعادة البيانات بأمان
- جدولة النسخ الاحتياطي التلقائي
- تشفير ملفات النسخ الاحتياطي

## 🛠️ التقنيات المستخدمة

- **Python 3.8+**: لغة البرمجة الأساسية
- **Tkinter**: واجهة المستخدم الرسومية
- **ttkbootstrap**: تحسين مظهر واجهة المستخدم
- **SQLite**: قاعدة البيانات المحلية
- **matplotlib**: الرسوم البيانية والتصور
- **pandas**: معالجة وتحليل البيانات
- **reportlab**: إنشاء تقارير PDF

## 📋 متطلبات النظام

- Python 3.8 أو أحدث
- نظام التشغيل: Windows 10/11, macOS, Linux
- ذاكرة: 4 GB RAM (الحد الأدنى)
- مساحة القرص: 500 MB

## 🚀 التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone https://github.com/your-repo/supply-management-system.git
cd supply-management-system
```

### 2. إنشاء بيئة افتراضية
```bash
python -m venv venv
```

### 3. تفعيل البيئة الافتراضية
**Windows:**
```bash
venv\Scripts\activate
```

**macOS/Linux:**
```bash
source venv/bin/activate
```

### 4. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 5. تشغيل النظام
```bash
python main.py
```

## 👤 المستخدم الافتراضي

عند التشغيل الأول، يتم إنشاء مستخدم افتراضي:
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

⚠️ **تنبيه أمني**: يُرجى تغيير كلمة المرور الافتراضية فور تسجيل الدخول الأول.

## 📁 هيكل المشروع

```
supply-management-system/
├── main.py                 # الملف الرئيسي للتطبيق
├── requirements.txt        # متطلبات المشروع
├── README.md              # ملف التوثيق
├── database/              # إدارة قاعدة البيانات
│   └── db_manager.py      # مدير قاعدة البيانات
├── modules/               # وحدات النظام
│   ├── dashboard.py       # لوحة التحكم
│   ├── basic_info.py      # البيانات الأساسية
│   ├── equipment.py       # إدارة المعدات
│   ├── items.py           # إدارة الأصناف
│   ├── users.py           # إدارة المستخدمين
│   └── backup.py          # النسخ الاحتياطي
└── utils/                 # أدوات مساعدة
    ├── language_manager.py # إدارة اللغات
    └── reports.py         # إنشاء التقارير
```

## 🔧 الإعدادات

يمكن تخصيص النظام من خلال:
- إعدادات قاعدة البيانات
- إعدادات النسخ الاحتياطي التلقائي
- إعدادات اللغة والمظهر
- إعدادات التقارير

## 📊 قاعدة البيانات

النظام يستخدم SQLite مع الجداول التالية:
- `users`: المستخدمين والصلاحيات
- `military_units`: الوحدات العسكرية
- `equipment`: المعدات والأجهزة
- `items`: الأصناف والمواد
- `suppliers`: الموردين
- `stock_movements`: حركات المخزون
- `supply_requests`: طلبات التموين
- `activity_log`: سجل النشاط

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم والتواصل

للدعم الفني أو الاستفسارات:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966-XX-XXX-XXXX

## 🔄 التحديثات

- **الإصدار 1.0.0**: الإصدار الأولي مع الميزات الأساسية
- **الإصدار 1.1.0**: إضافة ميزات التقارير المتقدمة (قريباً)
- **الإصدار 1.2.0**: دعم قواعد البيانات الخارجية (قريباً)

---

**تم تطوير هذا النظام خصيصاً لخدمة القوات العمومية وتحسين كفاءة إدارة التموين والمعدات.**
