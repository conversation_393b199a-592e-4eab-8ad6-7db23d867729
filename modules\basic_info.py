#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة المعلومات الأساسية
Basic Information Module for Supply Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime

class BasicInfoModule:
    """وحدة إدارة المعلومات الأساسية"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        
        # إنشاء الإطار الرئيسي
        self.frame = ttk_bs.Frame(parent)
        
        # متغيرات النموذج
        self.setup_variables()
        
        # إعداد واجهة المستخدم
        self.setup_ui()
        
        # تحميل البيانات
        self.load_data()
        
    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        self.unit_name_var = tk.StringVar()
        self.unit_code_var = tk.StringVar()
        self.unit_type_var = tk.StringVar()
        self.commander_name_var = tk.StringVar()
        self.location_var = tk.StringVar()
        self.phone_var = tk.StringVar()
        self.email_var = tk.StringVar()
        self.selected_unit_id = None
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # العنوان الرئيسي
        title_frame = ttk_bs.Frame(self.frame)
        title_frame.pack(fill=X, padx=10, pady=10)
        
        title_label = ttk_bs.Label(
            title_frame,
            text="🧩 إدارة المعلومات الأساسية - الوحدات العسكرية",
            font=("Arial", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack(side=LEFT)
        
        # إنشاء دفتر الملاحظات للأقسام
        self.notebook = ttk_bs.Notebook(self.frame)
        self.notebook.pack(fill=BOTH, expand=True, padx=10, pady=5)
        
        # قسم إدارة الوحدات العسكرية
        self.create_units_tab()
        
        # قسم إدارة الموردين
        self.create_suppliers_tab()
        
    def create_units_tab(self):
        """إنشاء قسم إدارة الوحدات العسكرية"""
        units_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(units_frame, text="🏢 الوحدات العسكرية")
        
        # تقسيم الشاشة
        main_paned = ttk_bs.PanedWindow(units_frame, orient=HORIZONTAL)
        main_paned.pack(fill=BOTH, expand=True, padx=5, pady=5)
        
        # الجانب الأيسر - نموذج الإدخال
        left_frame = ttk_bs.LabelFrame(main_paned, text="بيانات الوحدة العسكرية", padding=10)
        main_paned.add(left_frame, weight=1)
        
        self.create_unit_form(left_frame)
        
        # الجانب الأيمن - قائمة الوحدات
        right_frame = ttk_bs.LabelFrame(main_paned, text="قائمة الوحدات العسكرية", padding=10)
        main_paned.add(right_frame, weight=2)
        
        self.create_units_list(right_frame)
        
    def create_unit_form(self, parent):
        """إنشاء نموذج بيانات الوحدة العسكرية"""
        
        # اسم الوحدة
        ttk_bs.Label(parent, text="اسم الوحدة العسكرية:").grid(row=0, column=0, sticky=W, pady=5)
        unit_name_entry = ttk_bs.Entry(parent, textvariable=self.unit_name_var, width=30)
        unit_name_entry.grid(row=0, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # رمز الوحدة
        ttk_bs.Label(parent, text="رمز الوحدة:").grid(row=1, column=0, sticky=W, pady=5)
        unit_code_entry = ttk_bs.Entry(parent, textvariable=self.unit_code_var, width=30)
        unit_code_entry.grid(row=1, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # نوع الوحدة
        ttk_bs.Label(parent, text="نوع الوحدة:").grid(row=2, column=0, sticky=W, pady=5)
        unit_type_combo = ttk_bs.Combobox(
            parent,
            textvariable=self.unit_type_var,
            values=["فرقة", "لواء", "كتيبة", "سرية", "فصيلة", "مجموعة", "قاعدة", "مركز"],
            width=27,
            state="readonly"
        )
        unit_type_combo.grid(row=2, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # اسم القائد
        ttk_bs.Label(parent, text="اسم القائد:").grid(row=3, column=0, sticky=W, pady=5)
        commander_entry = ttk_bs.Entry(parent, textvariable=self.commander_name_var, width=30)
        commander_entry.grid(row=3, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # الموقع
        ttk_bs.Label(parent, text="الموقع:").grid(row=4, column=0, sticky=W, pady=5)
        location_entry = ttk_bs.Entry(parent, textvariable=self.location_var, width=30)
        location_entry.grid(row=4, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # رقم الهاتف
        ttk_bs.Label(parent, text="رقم الهاتف:").grid(row=5, column=0, sticky=W, pady=5)
        phone_entry = ttk_bs.Entry(parent, textvariable=self.phone_var, width=30)
        phone_entry.grid(row=5, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # البريد الإلكتروني
        ttk_bs.Label(parent, text="البريد الإلكتروني:").grid(row=6, column=0, sticky=W, pady=5)
        email_entry = ttk_bs.Entry(parent, textvariable=self.email_var, width=30)
        email_entry.grid(row=6, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # أزرار العمليات
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.grid(row=7, column=0, columnspan=2, pady=20)
        
        # زر إضافة
        add_btn = ttk_bs.Button(
            buttons_frame,
            text="➕ إضافة",
            bootstyle="success",
            command=self.add_unit
        )
        add_btn.pack(side=LEFT, padx=5)
        
        # زر تحديث
        update_btn = ttk_bs.Button(
            buttons_frame,
            text="✏️ تحديث",
            bootstyle="warning",
            command=self.update_unit
        )
        update_btn.pack(side=LEFT, padx=5)
        
        # زر حذف
        delete_btn = ttk_bs.Button(
            buttons_frame,
            text="🗑️ حذف",
            bootstyle="danger",
            command=self.delete_unit
        )
        delete_btn.pack(side=LEFT, padx=5)
        
        # زر مسح النموذج
        clear_btn = ttk_bs.Button(
            buttons_frame,
            text="🧹 مسح",
            bootstyle="secondary",
            command=self.clear_form
        )
        clear_btn.pack(side=LEFT, padx=5)
        
        # تكوين الأعمدة
        parent.grid_columnconfigure(1, weight=1)
        
    def create_units_list(self, parent):
        """إنشاء قائمة الوحدات العسكرية"""
        
        # إطار البحث
        search_frame = ttk_bs.Frame(parent)
        search_frame.pack(fill=X, pady=(0, 10))
        
        ttk_bs.Label(search_frame, text="البحث:").pack(side=LEFT)
        self.search_var = tk.StringVar()
        search_entry = ttk_bs.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side=LEFT, padx=(10, 5))
        search_entry.bind('<KeyRelease>', self.search_units)
        
        search_btn = ttk_bs.Button(
            search_frame,
            text="🔍",
            bootstyle="info-outline",
            command=self.search_units
        )
        search_btn.pack(side=LEFT)
        
        # جدول الوحدات
        columns = ("الرقم", "اسم الوحدة", "رمز الوحدة", "النوع", "القائد", "الموقع")
        self.units_tree = ttk.Treeview(
            parent,
            columns=columns,
            show="headings",
            height=15
        )
        
        # تكوين الأعمدة
        column_widths = [60, 200, 100, 100, 150, 150]
        for i, (col, width) in enumerate(zip(columns, column_widths)):
            self.units_tree.heading(col, text=col)
            self.units_tree.column(col, width=width, minwidth=50)
        
        # شريط التمرير
        units_scrollbar = ttk.Scrollbar(
            parent,
            orient=VERTICAL,
            command=self.units_tree.yview
        )
        self.units_tree.configure(yscrollcommand=units_scrollbar.set)
        
        # ربط الأحداث
        self.units_tree.bind('<Double-1>', self.on_unit_select)
        
        # تخطيط العناصر
        self.units_tree.pack(side=LEFT, fill=BOTH, expand=True)
        units_scrollbar.pack(side=RIGHT, fill=Y)
        
    def create_suppliers_tab(self):
        """إنشاء قسم إدارة الموردين"""
        suppliers_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(suppliers_frame, text="🏪 الموردين")
        
        # سيتم إضافة محتوى الموردين لاحقاً
        placeholder_label = ttk_bs.Label(
            suppliers_frame,
            text="قسم إدارة الموردين\nسيتم إضافة المحتوى قريباً",
            font=("Arial", 14),
            bootstyle="info"
        )
        placeholder_label.pack(expand=True)
        
    def add_unit(self):
        """إضافة وحدة عسكرية جديدة"""
        if not self.validate_unit_form():
            return
            
        try:
            query = """
            INSERT INTO military_units (unit_name, unit_code, unit_type, commander_name, location, phone, email)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            
            params = (
                self.unit_name_var.get(),
                self.unit_code_var.get(),
                self.unit_type_var.get(),
                self.commander_name_var.get(),
                self.location_var.get(),
                self.phone_var.get(),
                self.email_var.get()
            )
            
            self.db_manager.execute_query(query, params)
            
            messagebox.showinfo("نجح", "تم إضافة الوحدة العسكرية بنجاح")
            self.clear_form()
            self.load_units()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إضافة الوحدة: {str(e)}")
            
    def update_unit(self):
        """تحديث بيانات الوحدة العسكرية"""
        if not self.selected_unit_id:
            messagebox.showwarning("تحذير", "يرجى اختيار وحدة للتحديث")
            return
            
        if not self.validate_unit_form():
            return
            
        try:
            query = """
            UPDATE military_units 
            SET unit_name=?, unit_code=?, unit_type=?, commander_name=?, location=?, phone=?, email=?, updated_at=?
            WHERE id=?
            """
            
            params = (
                self.unit_name_var.get(),
                self.unit_code_var.get(),
                self.unit_type_var.get(),
                self.commander_name_var.get(),
                self.location_var.get(),
                self.phone_var.get(),
                self.email_var.get(),
                datetime.now().isoformat(),
                self.selected_unit_id
            )
            
            self.db_manager.execute_query(query, params)
            
            messagebox.showinfo("نجح", "تم تحديث بيانات الوحدة بنجاح")
            self.clear_form()
            self.load_units()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحديث الوحدة: {str(e)}")
            
    def delete_unit(self):
        """حذف الوحدة العسكرية"""
        if not self.selected_unit_id:
            messagebox.showwarning("تحذير", "يرجى اختيار وحدة للحذف")
            return
            
        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذه الوحدة؟"):
            try:
                query = "DELETE FROM military_units WHERE id=?"
                self.db_manager.execute_query(query, (self.selected_unit_id,))
                
                messagebox.showinfo("نجح", "تم حذف الوحدة بنجاح")
                self.clear_form()
                self.load_units()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في حذف الوحدة: {str(e)}")
                
    def validate_unit_form(self):
        """التحقق من صحة بيانات النموذج"""
        if not self.unit_name_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم الوحدة")
            return False
            
        if not self.unit_code_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال رمز الوحدة")
            return False
            
        if not self.unit_type_var.get().strip():
            messagebox.showerror("خطأ", "يرجى اختيار نوع الوحدة")
            return False
            
        return True
        
    def clear_form(self):
        """مسح النموذج"""
        self.unit_name_var.set("")
        self.unit_code_var.set("")
        self.unit_type_var.set("")
        self.commander_name_var.set("")
        self.location_var.set("")
        self.phone_var.set("")
        self.email_var.set("")
        self.selected_unit_id = None
        
    def load_units(self):
        """تحميل قائمة الوحدات العسكرية"""
        # مسح البيانات الحالية
        for item in self.units_tree.get_children():
            self.units_tree.delete(item)
            
        # جلب البيانات من قاعدة البيانات
        units = self.db_manager.fetch_all("SELECT * FROM military_units ORDER BY unit_name")
        
        # إضافة البيانات إلى الجدول
        for unit in units:
            self.units_tree.insert("", "end", values=(
                unit['id'],
                unit['unit_name'],
                unit['unit_code'],
                unit['unit_type'],
                unit['commander_name'] or "",
                unit['location'] or ""
            ))
            
    def search_units(self, event=None):
        """البحث في الوحدات العسكرية"""
        search_term = self.search_var.get().strip()
        
        if not search_term:
            self.load_units()
            return
            
        # مسح البيانات الحالية
        for item in self.units_tree.get_children():
            self.units_tree.delete(item)
            
        # البحث في قاعدة البيانات
        query = """
        SELECT * FROM military_units 
        WHERE unit_name LIKE ? OR unit_code LIKE ? OR commander_name LIKE ?
        ORDER BY unit_name
        """
        search_pattern = f"%{search_term}%"
        units = self.db_manager.fetch_all(query, (search_pattern, search_pattern, search_pattern))
        
        # إضافة النتائج إلى الجدول
        for unit in units:
            self.units_tree.insert("", "end", values=(
                unit['id'],
                unit['unit_name'],
                unit['unit_code'],
                unit['unit_type'],
                unit['commander_name'] or "",
                unit['location'] or ""
            ))
            
    def on_unit_select(self, event):
        """عند اختيار وحدة من القائمة"""
        selection = self.units_tree.selection()
        if selection:
            item = self.units_tree.item(selection[0])
            unit_id = item['values'][0]
            
            # جلب بيانات الوحدة
            unit = self.db_manager.fetch_one("SELECT * FROM military_units WHERE id=?", (unit_id,))
            
            if unit:
                # ملء النموذج بالبيانات
                self.unit_name_var.set(unit['unit_name'])
                self.unit_code_var.set(unit['unit_code'])
                self.unit_type_var.set(unit['unit_type'])
                self.commander_name_var.set(unit['commander_name'] or "")
                self.location_var.set(unit['location'] or "")
                self.phone_var.set(unit['phone'] or "")
                self.email_var.set(unit['email'] or "")
                self.selected_unit_id = unit['id']
                
    def load_data(self):
        """تحميل البيانات الأولية"""
        self.load_units()
