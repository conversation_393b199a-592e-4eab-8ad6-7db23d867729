# دليل البدء السريع - نظام إدارة التموين العام والقوة العمومية

## 🚀 التشغيل السريع

### الطريقة الأولى: التشغيل التلقائي

#### Windows
```bash
# تشغيل الملف التلقائي
run.bat
```

#### macOS/Linux
```bash
# إعطاء صلاحية التنفيذ
chmod +x run.sh

# تشغيل النظام
./run.sh
```

### الطريقة الثانية: تثبيت المتطلبات منفصلاً

#### Windows
```bash
# تثبيت المتطلبات أولاً
install_requirements.bat

# ثم تشغيل النظام
python main.py
```

#### macOS/Linux
```bash
# تثبيت المتطلبات أولاً
chmod +x install_requirements.sh
./install_requirements.sh

# ثم تشغيل النظام
python3 main.py
```

### التشغيل اليدوي
```bash
# 1. إنشاء البيئة الافتراضية
python -m venv venv

# 2. تفعيل البيئة الافتراضية
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# 3. تثبيت المتطلبات
pip install -r requirements.txt

# 4. تهيئة قاعدة البيانات
python init_database.py

# 5. تشغيل النظام
python main.py
```

## 👤 تسجيل الدخول

عند تشغيل النظام، ستظهر شاشة تسجيل الدخول أولاً.

**المستخدم الافتراضي:**
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

**ميزات شاشة تسجيل الدخول:**
- واجهة عربية أنيقة
- إمكانية الضغط على Enter لتسجيل الدخول
- رسائل خطأ واضحة
- عرض بيانات المستخدم الافتراضي

⚠️ **مهم**: يرجى تغيير كلمة المرور فور تسجيل الدخول الأول من خلال وحدة إدارة المستخدمين.

## 🔐 ميزات الأمان

### نظام تسجيل الدخول
- **شاشة تسجيل دخول آمنة**: تظهر عند تشغيل النظام
- **تشفير كلمات المرور**: باستخدام SHA-256
- **تسجيل الأنشطة**: تتبع جميع عمليات تسجيل الدخول والخروج
- **شريط حالة**: يعرض معلومات المستخدم الحالي والوقت
- **تسجيل خروج آمن**: مع إمكانية العودة لشاشة تسجيل الدخول

### خيارات المستخدم
- **تسجيل دخول**: للوصول للنظام الرئيسي
- **تسجيل خروج**: للعودة لشاشة تسجيل الدخول (ملف > تسجيل الخروج)
- **خروج**: لإغلاق النظام نهائياً (ملف > خروج)

## 📋 الوحدات المتاحة

### 🏠 لوحة التحكم
- عرض إحصائيات النظام
- رسوم بيانية للمخزون
- مراقبة طلبات التموين

### 📋 البيانات الأساسية
- إدارة الوحدات العسكرية
- إدارة الموردين
- تصنيف الجهات

### ⚙️ المعدات
- تسجيل المعدات الجديدة
- تتبع حالة المعدات
- إدارة الصيانة والضمان

### 📦 الأصناف
- إدارة المخزون
- تتبع مستويات المخزون
- تنبيهات المخزون المنخفض

### 👥 المستخدمين
- إدارة حسابات المستخدمين
- تعيين الصلاحيات
- تتبع نشاط المستخدمين

### 💾 النسخ الاحتياطي
- إنشاء نسخ احتياطية
- استعادة البيانات
- جدولة النسخ التلقائي

## 🔧 استكشاف الأخطاء

### مشكلة: "Python غير مثبت"
**الحل**: تثبيت Python 3.8 أو أحدث من [python.org](https://python.org)

### مشكلة: "خطأ في تثبيت المتطلبات"
**الحل**: 
```bash
pip install --upgrade pip
pip install -r requirements.txt --force-reinstall
```

### مشكلة: "خطأ في قاعدة البيانات"
**الحل**: 
```bash
# حذف قاعدة البيانات وإعادة إنشائها
del supply_management.db  # Windows
rm supply_management.db   # macOS/Linux

# إعادة تهيئة قاعدة البيانات
python init_database.py
```

### مشكلة: "خطأ في عرض الخطوط العربية"
**الحل**: تأكد من تثبيت خطوط عربية على النظام

## 📞 الدعم

للمساعدة أو الإبلاغ عن مشاكل:
- راجع ملف README.md للتفاصيل الكاملة
- تحقق من ملفات السجل في مجلد logs/
- تأكد من تحديث النظام لآخر إصدار

## 🔄 التحديثات

لتحديث النظام:
1. احفظ نسخة احتياطية من البيانات
2. حمل آخر إصدار من المشروع
3. شغل `python init_database.py` لتحديث قاعدة البيانات
4. استعد النسخة الاحتياطية إذا لزم الأمر

---

**نصيحة**: استخدم ميزة النسخ الاحتياطي التلقائي لحماية بياناتك!
