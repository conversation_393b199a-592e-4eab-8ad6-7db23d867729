#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تهيئة قاعدة البيانات
Database Initialization Script
"""

from database.db_manager import DatabaseManager

def main():
    """تهيئة قاعدة البيانات وإنشاء الجداول"""
    print("بدء تهيئة قاعدة البيانات...")
    
    try:
        # إنشاء مدير قاعدة البيانات
        db_manager = DatabaseManager()
        
        # إنشاء الجداول
        print("إنشاء الجداول...")
        db_manager.create_tables()
        
        # إنشاء المستخدم الافتراضي
        print("إنشاء المستخدم الافتراضي...")
        db_manager.create_default_user()
        
        print("تم تهيئة قاعدة البيانات بنجاح!")
        print("المستخدم الافتراضي:")
        print("اسم المستخدم: admin")
        print("كلمة المرور: admin123")
        print("يرجى تغيير كلمة المرور بعد تسجيل الدخول الأول.")
        
    except Exception as e:
        print(f"خطأ في تهيئة قاعدة البيانات: {e}")
        return False
        
    return True

if __name__ == "__main__":
    main()
