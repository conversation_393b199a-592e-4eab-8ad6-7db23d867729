#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات
Database Manager for Supply Management System
"""

import sqlite3
import os
from datetime import datetime
import json
import hashlib

class DatabaseManager:
    """مدير قاعدة البيانات"""
    
    def __init__(self, db_path="data/supply_system.db"):
        self.db_path = db_path
        self.ensure_data_directory()
        self.connection = None
        
    def ensure_data_directory(self):
        """التأكد من وجود مجلد البيانات"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row
            return self.connection
        except Exception as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return None
            
    def disconnect(self):
        """قطع الاتصال بقاعدة البيانات"""
        if self.connection:
            self.connection.close()
            
    def execute_query(self, query, params=None):
        """تنفيذ استعلام"""
        try:
            conn = self.connect()
            cursor = conn.cursor()
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
                
            conn.commit()
            return cursor
        except Exception as e:
            print(f"خطأ في تنفيذ الاستعلام: {e}")
            return None
        finally:
            self.disconnect()
            
    def fetch_all(self, query, params=None):
        """جلب جميع النتائج"""
        try:
            conn = self.connect()
            cursor = conn.cursor()
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
                
            results = cursor.fetchall()
            return [dict(row) for row in results]
        except Exception as e:
            print(f"خطأ في جلب البيانات: {e}")
            return []
        finally:
            self.disconnect()
            
    def fetch_one(self, query, params=None):
        """جلب نتيجة واحدة"""
        try:
            conn = self.connect()
            cursor = conn.cursor()
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
                
            result = cursor.fetchone()
            return dict(result) if result else None
        except Exception as e:
            print(f"خطأ في جلب البيانات: {e}")
            return None
        finally:
            self.disconnect()
            
    def create_tables(self):
        """إنشاء جداول قاعدة البيانات"""
        
        # جدول المستخدمين
        users_table = """
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            full_name TEXT NOT NULL,
            email TEXT,
            role TEXT DEFAULT 'user',
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP
        )
        """
        
        # جدول الوحدات العسكرية
        units_table = """
        CREATE TABLE IF NOT EXISTS military_units (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            unit_name TEXT NOT NULL,
            unit_code TEXT UNIQUE NOT NULL,
            unit_type TEXT NOT NULL,
            commander_name TEXT,
            location TEXT,
            phone TEXT,
            email TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        
        # جدول المعدات
        equipment_table = """
        CREATE TABLE IF NOT EXISTS equipment (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            equipment_name TEXT NOT NULL,
            equipment_code TEXT UNIQUE NOT NULL,
            category TEXT NOT NULL,
            model TEXT,
            manufacturer TEXT,
            serial_number TEXT,
            purchase_date DATE,
            warranty_date DATE,
            status TEXT DEFAULT 'active',
            unit_id INTEGER,
            location TEXT,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (unit_id) REFERENCES military_units (id)
        )
        """
        
        # جدول الأصناف
        items_table = """
        CREATE TABLE IF NOT EXISTS items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            item_name TEXT NOT NULL,
            item_code TEXT UNIQUE NOT NULL,
            category TEXT NOT NULL,
            unit_of_measure TEXT NOT NULL,
            minimum_stock INTEGER DEFAULT 0,
            maximum_stock INTEGER DEFAULT 0,
            current_stock INTEGER DEFAULT 0,
            unit_price REAL DEFAULT 0.0,
            supplier TEXT,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        
        # جدول حركات المخزون
        stock_movements_table = """
        CREATE TABLE IF NOT EXISTS stock_movements (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            item_id INTEGER NOT NULL,
            movement_type TEXT NOT NULL, -- 'in', 'out', 'transfer'
            quantity INTEGER NOT NULL,
            unit_price REAL DEFAULT 0.0,
            total_value REAL DEFAULT 0.0,
            from_unit_id INTEGER,
            to_unit_id INTEGER,
            reference_number TEXT,
            notes TEXT,
            created_by INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (item_id) REFERENCES items (id),
            FOREIGN KEY (from_unit_id) REFERENCES military_units (id),
            FOREIGN KEY (to_unit_id) REFERENCES military_units (id),
            FOREIGN KEY (created_by) REFERENCES users (id)
        )
        """
        
        # جدول الموردين
        suppliers_table = """
        CREATE TABLE IF NOT EXISTS suppliers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            supplier_name TEXT NOT NULL,
            supplier_code TEXT UNIQUE NOT NULL,
            contact_person TEXT,
            phone TEXT,
            email TEXT,
            address TEXT,
            tax_number TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        
        # جدول طلبات التوريد
        supply_requests_table = """
        CREATE TABLE IF NOT EXISTS supply_requests (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            request_number TEXT UNIQUE NOT NULL,
            requesting_unit_id INTEGER NOT NULL,
            request_date DATE NOT NULL,
            required_date DATE,
            status TEXT DEFAULT 'pending', -- 'pending', 'approved', 'rejected', 'completed'
            priority TEXT DEFAULT 'normal', -- 'low', 'normal', 'high', 'urgent'
            notes TEXT,
            created_by INTEGER,
            approved_by INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (requesting_unit_id) REFERENCES military_units (id),
            FOREIGN KEY (created_by) REFERENCES users (id),
            FOREIGN KEY (approved_by) REFERENCES users (id)
        )
        """
        
        # جدول تفاصيل طلبات التوريد
        supply_request_items_table = """
        CREATE TABLE IF NOT EXISTS supply_request_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            request_id INTEGER NOT NULL,
            item_id INTEGER NOT NULL,
            requested_quantity INTEGER NOT NULL,
            approved_quantity INTEGER DEFAULT 0,
            unit_price REAL DEFAULT 0.0,
            total_price REAL DEFAULT 0.0,
            notes TEXT,
            FOREIGN KEY (request_id) REFERENCES supply_requests (id),
            FOREIGN KEY (item_id) REFERENCES items (id)
        )
        """
        
        # جدول الإعدادات
        settings_table = """
        CREATE TABLE IF NOT EXISTS settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            setting_key TEXT UNIQUE NOT NULL,
            setting_value TEXT,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        
        # جدول سجل النشاطات
        activity_log_table = """
        CREATE TABLE IF NOT EXISTS activity_log (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            action TEXT NOT NULL,
            table_name TEXT,
            record_id INTEGER,
            old_values TEXT,
            new_values TEXT,
            ip_address TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        """
        
        # تنفيذ إنشاء الجداول
        tables = [
            users_table,
            units_table,
            equipment_table,
            items_table,
            stock_movements_table,
            suppliers_table,
            supply_requests_table,
            supply_request_items_table,
            settings_table,
            activity_log_table
        ]
        
        for table in tables:
            self.execute_query(table)
            
        # إدراج البيانات الأولية
        self.insert_initial_data()
        
    def insert_initial_data(self):
        """إدراج البيانات الأولية"""
        
        # إنشاء مستخدم المدير الافتراضي
        admin_password = self.hash_password("admin123")
        admin_user = """
        INSERT OR IGNORE INTO users (username, password_hash, full_name, role)
        VALUES (?, ?, ?, ?)
        """
        self.execute_query(admin_user, ("admin", admin_password, "مدير النظام", "admin"))
        
        # إعدادات النظام الافتراضية
        default_settings = [
            ("system_name", "نظام إدارة التموين العام والقوة العمومية", "اسم النظام"),
            ("default_language", "ar", "اللغة الافتراضية"),
            ("currency", "ريال سعودي", "العملة المستخدمة"),
            ("backup_interval", "daily", "فترة النسخ الاحتياطي"),
            ("session_timeout", "30", "انتهاء الجلسة بالدقائق")
        ]
        
        for key, value, desc in default_settings:
            setting_query = """
            INSERT OR IGNORE INTO settings (setting_key, setting_value, description)
            VALUES (?, ?, ?)
            """
            self.execute_query(setting_query, (key, value, desc))

    def create_default_user(self):
        """إنشاء المستخدم الافتراضي"""
        try:
            # التحقق من وجود مستخدمين
            existing_users = self.fetch_all("SELECT COUNT(*) as count FROM users")
            if existing_users and existing_users[0]['count'] > 0:
                print("يوجد مستخدمون في النظام بالفعل")
                return True

            # إنشاء المستخدم الافتراضي
            admin_password = self.hash_password("admin123")
            admin_user = """
            INSERT INTO users (username, password_hash, full_name, role, is_active)
            VALUES (?, ?, ?, ?, ?)
            """
            self.execute_query(admin_user, ("admin", admin_password, "مدير النظام", "admin", 1))
            print("تم إنشاء المستخدم الافتراضي بنجاح")
            return True

        except Exception as e:
            print(f"خطأ في إنشاء المستخدم الافتراضي: {e}")
            return False

    def hash_password(self, password):
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()
        
    def verify_password(self, password, hashed):
        """التحقق من كلمة المرور"""
        return self.hash_password(password) == hashed
        
    def log_activity(self, user_id, action, table_name=None, record_id=None, old_values=None, new_values=None):
        """تسجيل النشاط"""
        query = """
        INSERT INTO activity_log (user_id, action, table_name, record_id, old_values, new_values)
        VALUES (?, ?, ?, ?, ?, ?)
        """
        
        old_json = json.dumps(old_values) if old_values else None
        new_json = json.dumps(new_values) if new_values else None
        
        self.execute_query(query, (user_id, action, table_name, record_id, old_json, new_json))
        
    def backup_database(self, backup_path):
        """نسخ احتياطي لقاعدة البيانات"""
        try:
            import shutil
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = f"{backup_path}/backup_{timestamp}.db"
            shutil.copy2(self.db_path, backup_file)
            return backup_file
        except Exception as e:
            print(f"خطأ في النسخ الاحتياطي: {e}")
            return None
            
    def restore_database(self, backup_file):
        """استعادة قاعدة البيانات من نسخة احتياطية"""
        try:
            import shutil
            shutil.copy2(backup_file, self.db_path)
            return True
        except Exception as e:
            print(f"خطأ في استعادة النسخة الاحتياطية: {e}")
            return False
