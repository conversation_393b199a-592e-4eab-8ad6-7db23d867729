#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة إدارة الأصناف
Items Management Module for Supply Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime

class ItemsModule:
    """وحدة إدارة الأصناف والمخزون"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        
        # إنشاء الإطار الرئيسي
        self.frame = ttk_bs.Frame(parent)
        
        # متغيرات النموذج
        self.setup_variables()
        
        # إعداد واجهة المستخدم
        self.setup_ui()
        
        # تحميل البيانات
        self.load_data()
        
    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        self.item_name_var = tk.StringVar()
        self.item_code_var = tk.StringVar()
        self.category_var = tk.StringVar()
        self.unit_of_measure_var = tk.StringVar()
        self.minimum_stock_var = tk.IntVar()
        self.maximum_stock_var = tk.IntVar()
        self.current_stock_var = tk.IntVar()
        self.unit_price_var = tk.DoubleVar()
        self.supplier_var = tk.StringVar()
        self.description_var = tk.StringVar()
        self.selected_item_id = None
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # العنوان الرئيسي
        title_frame = ttk_bs.Frame(self.frame)
        title_frame.pack(fill=X, padx=10, pady=10)
        
        title_label = ttk_bs.Label(
            title_frame,
            text="📦 إدارة الأصناف والمخزون",
            font=("Arial", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack(side=LEFT)
        
        # أزرار سريعة
        quick_buttons_frame = ttk_bs.Frame(title_frame)
        quick_buttons_frame.pack(side=RIGHT)
        
        stock_btn = ttk_bs.Button(
            quick_buttons_frame,
            text="📊 حركة المخزون",
            bootstyle="info-outline",
            command=self.show_stock_movements
        )
        stock_btn.pack(side=LEFT, padx=5)
        
        low_stock_btn = ttk_bs.Button(
            quick_buttons_frame,
            text="⚠️ مخزون منخفض",
            bootstyle="warning-outline",
            command=self.show_low_stock
        )
        low_stock_btn.pack(side=LEFT, padx=5)
        
        # إنشاء دفتر الملاحظات للأقسام
        self.notebook = ttk_bs.Notebook(self.frame)
        self.notebook.pack(fill=BOTH, expand=True, padx=10, pady=5)
        
        # قسم إدارة الأصناف
        self.create_items_tab()
        
        # قسم حركات المخزون
        self.create_stock_movements_tab()
        
    def create_items_tab(self):
        """إنشاء قسم إدارة الأصناف"""
        items_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(items_frame, text="📦 الأصناف")
        
        # تقسيم الشاشة
        main_paned = ttk_bs.PanedWindow(items_frame, orient=HORIZONTAL)
        main_paned.pack(fill=BOTH, expand=True, padx=5, pady=5)
        
        # الجانب الأيسر - نموذج الإدخال
        left_frame = ttk_bs.LabelFrame(main_paned, text="بيانات الصنف", padding=10)
        main_paned.add(left_frame, weight=1)
        
        self.create_item_form(left_frame)
        
        # الجانب الأيمن - قائمة الأصناف
        right_frame = ttk_bs.LabelFrame(main_paned, text="قائمة الأصناف", padding=10)
        main_paned.add(right_frame, weight=2)
        
        self.create_items_list(right_frame)
        
    def create_item_form(self, parent):
        """إنشاء نموذج بيانات الصنف"""
        
        # اسم الصنف
        ttk_bs.Label(parent, text="اسم الصنف:").grid(row=0, column=0, sticky=W, pady=5)
        item_name_entry = ttk_bs.Entry(parent, textvariable=self.item_name_var, width=30)
        item_name_entry.grid(row=0, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # رمز الصنف
        ttk_bs.Label(parent, text="رمز الصنف:").grid(row=1, column=0, sticky=W, pady=5)
        item_code_entry = ttk_bs.Entry(parent, textvariable=self.item_code_var, width=30)
        item_code_entry.grid(row=1, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # فئة الصنف
        ttk_bs.Label(parent, text="الفئة:").grid(row=2, column=0, sticky=W, pady=5)
        category_combo = ttk_bs.Combobox(
            parent,
            textvariable=self.category_var,
            values=["ذخيرة", "مواد غذائية", "وقود", "قطع غيار", "مواد طبية", "مواد كيميائية", "أدوات", "أخرى"],
            width=27,
            state="readonly"
        )
        category_combo.grid(row=2, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # وحدة القياس
        ttk_bs.Label(parent, text="وحدة القياس:").grid(row=3, column=0, sticky=W, pady=5)
        unit_combo = ttk_bs.Combobox(
            parent,
            textvariable=self.unit_of_measure_var,
            values=["قطعة", "كيلوجرام", "لتر", "متر", "صندوق", "كرتون", "طن", "جالون"],
            width=27,
            state="readonly"
        )
        unit_combo.grid(row=3, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # الحد الأدنى للمخزون
        ttk_bs.Label(parent, text="الحد الأدنى للمخزون:").grid(row=4, column=0, sticky=W, pady=5)
        min_stock_entry = ttk_bs.Entry(parent, textvariable=self.minimum_stock_var, width=30)
        min_stock_entry.grid(row=4, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # الحد الأقصى للمخزون
        ttk_bs.Label(parent, text="الحد الأقصى للمخزون:").grid(row=5, column=0, sticky=W, pady=5)
        max_stock_entry = ttk_bs.Entry(parent, textvariable=self.maximum_stock_var, width=30)
        max_stock_entry.grid(row=5, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # المخزون الحالي
        ttk_bs.Label(parent, text="المخزون الحالي:").grid(row=6, column=0, sticky=W, pady=5)
        current_stock_entry = ttk_bs.Entry(parent, textvariable=self.current_stock_var, width=30)
        current_stock_entry.grid(row=6, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # سعر الوحدة
        ttk_bs.Label(parent, text="سعر الوحدة:").grid(row=7, column=0, sticky=W, pady=5)
        price_entry = ttk_bs.Entry(parent, textvariable=self.unit_price_var, width=30)
        price_entry.grid(row=7, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # المورد
        ttk_bs.Label(parent, text="المورد:").grid(row=8, column=0, sticky=W, pady=5)
        supplier_entry = ttk_bs.Entry(parent, textvariable=self.supplier_var, width=30)
        supplier_entry.grid(row=8, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # الوصف
        ttk_bs.Label(parent, text="الوصف:").grid(row=9, column=0, sticky=W+N, pady=5)
        self.description_text = tk.Text(parent, width=30, height=3)
        self.description_text.grid(row=9, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # أزرار العمليات
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.grid(row=10, column=0, columnspan=2, pady=20)
        
        # زر إضافة
        add_btn = ttk_bs.Button(
            buttons_frame,
            text="➕ إضافة",
            bootstyle="success",
            command=self.add_item
        )
        add_btn.pack(side=LEFT, padx=5)
        
        # زر تحديث
        update_btn = ttk_bs.Button(
            buttons_frame,
            text="✏️ تحديث",
            bootstyle="warning",
            command=self.update_item
        )
        update_btn.pack(side=LEFT, padx=5)
        
        # زر حذف
        delete_btn = ttk_bs.Button(
            buttons_frame,
            text="🗑️ حذف",
            bootstyle="danger",
            command=self.delete_item
        )
        delete_btn.pack(side=LEFT, padx=5)
        
        # زر مسح النموذج
        clear_btn = ttk_bs.Button(
            buttons_frame,
            text="🧹 مسح",
            bootstyle="secondary",
            command=self.clear_form
        )
        clear_btn.pack(side=LEFT, padx=5)
        
        # تكوين الأعمدة
        parent.grid_columnconfigure(1, weight=1)
        
    def create_items_list(self, parent):
        """إنشاء قائمة الأصناف"""
        
        # إطار البحث والفلترة
        search_frame = ttk_bs.Frame(parent)
        search_frame.pack(fill=X, pady=(0, 10))
        
        # البحث
        ttk_bs.Label(search_frame, text="البحث:").pack(side=LEFT)
        self.search_var = tk.StringVar()
        search_entry = ttk_bs.Entry(search_frame, textvariable=self.search_var, width=20)
        search_entry.pack(side=LEFT, padx=(10, 5))
        search_entry.bind('<KeyRelease>', self.search_items)
        
        # فلتر الفئة
        ttk_bs.Label(search_frame, text="الفئة:").pack(side=LEFT, padx=(20, 5))
        self.filter_category_var = tk.StringVar()
        filter_combo = ttk_bs.Combobox(
            search_frame,
            textvariable=self.filter_category_var,
            values=["الكل", "ذخيرة", "مواد غذائية", "وقود", "قطع غيار", "مواد طبية", "مواد كيميائية", "أدوات", "أخرى"],
            width=15,
            state="readonly"
        )
        filter_combo.pack(side=LEFT, padx=5)
        filter_combo.bind('<<ComboboxSelected>>', self.filter_items)
        filter_combo.set("الكل")
        
        # جدول الأصناف
        columns = ("الرقم", "اسم الصنف", "الرمز", "الفئة", "المخزون الحالي", "الحد الأدنى", "الوحدة", "السعر")
        self.items_tree = ttk.Treeview(
            parent,
            columns=columns,
            show="headings",
            height=15
        )
        
        # تكوين الأعمدة
        column_widths = [60, 200, 100, 120, 100, 100, 80, 100]
        for i, (col, width) in enumerate(zip(columns, column_widths)):
            self.items_tree.heading(col, text=col)
            self.items_tree.column(col, width=width, minwidth=50)
        
        # تلوين الصفوف حسب حالة المخزون
        self.items_tree.tag_configure('low_stock', background='#ffcccc')
        self.items_tree.tag_configure('normal_stock', background='#ccffcc')
        self.items_tree.tag_configure('high_stock', background='#ffffcc')
        
        # شريط التمرير
        items_scrollbar = ttk.Scrollbar(
            parent,
            orient=VERTICAL,
            command=self.items_tree.yview
        )
        self.items_tree.configure(yscrollcommand=items_scrollbar.set)
        
        # ربط الأحداث
        self.items_tree.bind('<Double-1>', self.on_item_select)
        
        # تخطيط العناصر
        self.items_tree.pack(side=LEFT, fill=BOTH, expand=True)
        items_scrollbar.pack(side=RIGHT, fill=Y)
        
    def create_stock_movements_tab(self):
        """إنشاء قسم حركات المخزون"""
        movements_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(movements_frame, text="📊 حركات المخزون")
        
        # سيتم إضافة محتوى حركات المخزون لاحقاً
        placeholder_label = ttk_bs.Label(
            movements_frame,
            text="قسم حركات المخزون\nسيتم إضافة المحتوى قريباً",
            font=("Arial", 14),
            bootstyle="info"
        )
        placeholder_label.pack(expand=True)

    def add_item(self):
        """إضافة صنف جديد"""
        if not self.validate_item_form():
            return

        try:
            query = """
            INSERT INTO items (item_name, item_code, category, unit_of_measure, minimum_stock,
                             maximum_stock, current_stock, unit_price, supplier, description)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            params = (
                self.item_name_var.get(),
                self.item_code_var.get(),
                self.category_var.get(),
                self.unit_of_measure_var.get(),
                self.minimum_stock_var.get(),
                self.maximum_stock_var.get(),
                self.current_stock_var.get(),
                self.unit_price_var.get(),
                self.supplier_var.get(),
                self.description_text.get("1.0", tk.END).strip()
            )

            self.db_manager.execute_query(query, params)

            messagebox.showinfo("نجح", "تم إضافة الصنف بنجاح")
            self.clear_form()
            self.load_items()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إضافة الصنف: {str(e)}")

    def update_item(self):
        """تحديث بيانات الصنف"""
        if not self.selected_item_id:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف للتحديث")
            return

        if not self.validate_item_form():
            return

        try:
            query = """
            UPDATE items
            SET item_name=?, item_code=?, category=?, unit_of_measure=?, minimum_stock=?,
                maximum_stock=?, current_stock=?, unit_price=?, supplier=?, description=?, updated_at=?
            WHERE id=?
            """

            params = (
                self.item_name_var.get(),
                self.item_code_var.get(),
                self.category_var.get(),
                self.unit_of_measure_var.get(),
                self.minimum_stock_var.get(),
                self.maximum_stock_var.get(),
                self.current_stock_var.get(),
                self.unit_price_var.get(),
                self.supplier_var.get(),
                self.description_text.get("1.0", tk.END).strip(),
                datetime.now().isoformat(),
                self.selected_item_id
            )

            self.db_manager.execute_query(query, params)

            messagebox.showinfo("نجح", "تم تحديث بيانات الصنف بنجاح")
            self.clear_form()
            self.load_items()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحديث الصنف: {str(e)}")

    def delete_item(self):
        """حذف الصنف"""
        if not self.selected_item_id:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف للحذف")
            return

        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذا الصنف؟"):
            try:
                query = "DELETE FROM items WHERE id=?"
                self.db_manager.execute_query(query, (self.selected_item_id,))

                messagebox.showinfo("نجح", "تم حذف الصنف بنجاح")
                self.clear_form()
                self.load_items()

            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في حذف الصنف: {str(e)}")

    def validate_item_form(self):
        """التحقق من صحة بيانات النموذج"""
        if not self.item_name_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم الصنف")
            return False

        if not self.item_code_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال رمز الصنف")
            return False

        if not self.category_var.get().strip():
            messagebox.showerror("خطأ", "يرجى اختيار فئة الصنف")
            return False

        if not self.unit_of_measure_var.get().strip():
            messagebox.showerror("خطأ", "يرجى اختيار وحدة القياس")
            return False

        return True

    def clear_form(self):
        """مسح النموذج"""
        self.item_name_var.set("")
        self.item_code_var.set("")
        self.category_var.set("")
        self.unit_of_measure_var.set("")
        self.minimum_stock_var.set(0)
        self.maximum_stock_var.set(0)
        self.current_stock_var.set(0)
        self.unit_price_var.set(0.0)
        self.supplier_var.set("")
        self.description_text.delete("1.0", tk.END)
        self.selected_item_id = None

    def load_items(self):
        """تحميل قائمة الأصناف"""
        # مسح البيانات الحالية
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)

        # جلب البيانات من قاعدة البيانات
        items_list = self.db_manager.fetch_all("SELECT * FROM items ORDER BY item_name")

        # إضافة البيانات إلى الجدول
        for item in items_list:
            # تحديد لون الصف حسب حالة المخزون
            tag = self.get_stock_status_tag(item['current_stock'], item['minimum_stock'], item['maximum_stock'])

            self.items_tree.insert("", "end", values=(
                item['id'],
                item['item_name'],
                item['item_code'],
                item['category'],
                item['current_stock'],
                item['minimum_stock'],
                item['unit_of_measure'],
                f"{item['unit_price']:.2f}"
            ), tags=(tag,))

    def get_stock_status_tag(self, current, minimum, maximum):
        """تحديد حالة المخزون"""
        if current <= minimum:
            return 'low_stock'
        elif current >= maximum:
            return 'high_stock'
        else:
            return 'normal_stock'

    def search_items(self, event=None):
        """البحث في الأصناف"""
        search_term = self.search_var.get().strip()
        category_filter = self.filter_category_var.get()

        # بناء الاستعلام
        query = "SELECT * FROM items WHERE 1=1"
        params = []

        if search_term:
            query += " AND (item_name LIKE ? OR item_code LIKE ? OR supplier LIKE ?)"
            search_pattern = f"%{search_term}%"
            params.extend([search_pattern, search_pattern, search_pattern])

        if category_filter and category_filter != "الكل":
            query += " AND category = ?"
            params.append(category_filter)

        query += " ORDER BY item_name"

        # مسح البيانات الحالية
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)

        # جلب النتائج
        items_list = self.db_manager.fetch_all(query, params)

        # إضافة النتائج إلى الجدول
        for item in items_list:
            tag = self.get_stock_status_tag(item['current_stock'], item['minimum_stock'], item['maximum_stock'])

            self.items_tree.insert("", "end", values=(
                item['id'],
                item['item_name'],
                item['item_code'],
                item['category'],
                item['current_stock'],
                item['minimum_stock'],
                item['unit_of_measure'],
                f"{item['unit_price']:.2f}"
            ), tags=(tag,))

    def filter_items(self, event=None):
        """فلترة الأصناف حسب الفئة"""
        self.search_items()

    def on_item_select(self, event):
        """عند اختيار صنف من القائمة"""
        selection = self.items_tree.selection()
        if selection:
            item = self.items_tree.item(selection[0])
            item_id = item['values'][0]

            # جلب بيانات الصنف
            item_data = self.db_manager.fetch_one("SELECT * FROM items WHERE id=?", (item_id,))

            if item_data:
                # ملء النموذج بالبيانات
                self.item_name_var.set(item_data['item_name'])
                self.item_code_var.set(item_data['item_code'])
                self.category_var.set(item_data['category'])
                self.unit_of_measure_var.set(item_data['unit_of_measure'])
                self.minimum_stock_var.set(item_data['minimum_stock'])
                self.maximum_stock_var.set(item_data['maximum_stock'])
                self.current_stock_var.set(item_data['current_stock'])
                self.unit_price_var.set(item_data['unit_price'])
                self.supplier_var.set(item_data['supplier'] or "")

                # ملء الوصف
                self.description_text.delete("1.0", tk.END)
                if item_data['description']:
                    self.description_text.insert("1.0", item_data['description'])

                self.selected_item_id = item_data['id']

    def show_stock_movements(self):
        """عرض حركات المخزون"""
        # التبديل إلى قسم حركات المخزون
        self.notebook.select(1)

    def show_low_stock(self):
        """عرض الأصناف منخفضة المخزون"""
        # مسح البيانات الحالية
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)

        # جلب الأصناف منخفضة المخزون
        low_stock_items = self.db_manager.fetch_all(
            "SELECT * FROM items WHERE current_stock <= minimum_stock ORDER BY item_name"
        )

        # إضافة النتائج إلى الجدول
        for item in low_stock_items:
            self.items_tree.insert("", "end", values=(
                item['id'],
                item['item_name'],
                item['item_code'],
                item['category'],
                item['current_stock'],
                item['minimum_stock'],
                item['unit_of_measure'],
                f"{item['unit_price']:.2f}"
            ), tags=('low_stock',))

        if not low_stock_items:
            messagebox.showinfo("معلومات", "لا توجد أصناف منخفضة المخزون")
        else:
            messagebox.showinfo("تنبيه", f"يوجد {len(low_stock_items)} صنف منخفض المخزون")

    def load_data(self):
        """تحميل البيانات الأولية"""
        self.load_items()
