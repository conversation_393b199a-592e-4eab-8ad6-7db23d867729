@echo off
chcp 65001 > nul
title نظام إدارة التموين العام والقوة العمومية

echo ========================================
echo    نظام إدارة التموين العام والقوة العمومية
echo    Supply Management System
echo ========================================
echo.

echo جاري التحقق من Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث من https://python.org
    pause
    exit /b 1
)

echo جاري التحقق من المتطلبات...
if not exist "venv" (
    echo إنشاء البيئة الافتراضية...
    python -m venv venv
)

echo تفعيل البيئة الافتراضية...
call venv\Scripts\activate.bat

echo تثبيت المتطلبات...
pip install -r requirements.txt --quiet

echo تهيئة قاعدة البيانات...
python init_database.py

echo.
echo بدء تشغيل النظام...
echo.
python main.py

if errorlevel 1 (
    echo.
    echo حدث خطأ أثناء تشغيل النظام
    pause
)

deactivate
