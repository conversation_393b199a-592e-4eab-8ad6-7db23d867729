#!/bin/bash

# تثبيت متطلبات نظام إدارة التموين العام والقوة العمومية

echo "========================================"
echo "   تثبيت متطلبات نظام إدارة التموين"
echo "========================================"
echo

# التحقق من Python
echo "جاري التحقق من Python..."
if ! command -v python3 &> /dev/null; then
    echo "خطأ: Python3 غير مثبت على النظام"
    echo "يرجى تثبيت Python 3.8 أو أحدث"
    exit 1
fi

echo "جاري تثبيت المتطلبات الأساسية..."
pip3 install --upgrade pip

echo "تثبيت ttkbootstrap..."
pip3 install ttkbootstrap

echo "تثبيت matplotlib..."
pip3 install matplotlib

echo "تثبيت tkcalendar..."
pip3 install tkcalendar

echo "تثبيت pandas..."
pip3 install pandas

echo "تثبيت openpyxl..."
pip3 install openpyxl

echo "تثبيت reportlab..."
pip3 install reportlab

echo "تثبيت xlsxwriter..."
pip3 install xlsxwriter

echo "تثبيت python-dateutil..."
pip3 install python-dateutil

echo "تثبيت Pillow..."
pip3 install Pillow

echo
echo "تم تثبيت جميع المتطلبات بنجاح!"
echo "يمكنك الآن تشغيل النظام باستخدام: python3 main.py"
echo
