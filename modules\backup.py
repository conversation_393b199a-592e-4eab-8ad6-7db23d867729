#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة النسخ الاحتياطي والاستعادة
Backup and Restore Module for Supply Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime
import os
import shutil
import sqlite3
import json

class BackupModule:
    """وحدة النسخ الاحتياطي والاستعادة"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        
        # إنشاء الإطار الرئيسي
        self.frame = ttk_bs.Frame(parent)
        
        # إعداد واجهة المستخدم
        self.setup_ui()
        
        # تحميل البيانات
        self.load_backup_history()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # العنوان الرئيسي
        title_frame = ttk_bs.Frame(self.frame)
        title_frame.pack(fill=X, padx=10, pady=10)
        
        title_label = ttk_bs.Label(
            title_frame,
            text="💾 النسخ الاحتياطي والاستعادة",
            font=("Arial", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack(side=LEFT)
        
        # إنشاء دفتر الملاحظات للأقسام
        self.notebook = ttk_bs.Notebook(self.frame)
        self.notebook.pack(fill=BOTH, expand=True, padx=10, pady=5)
        
        # قسم النسخ الاحتياطي
        self.create_backup_tab()
        
        # قسم الاستعادة
        self.create_restore_tab()
        
        # قسم الإعدادات
        self.create_settings_tab()
        
    def create_backup_tab(self):
        """إنشاء قسم النسخ الاحتياطي"""
        backup_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(backup_frame, text="💾 النسخ الاحتياطي")
        
        # قسم إنشاء نسخة احتياطية
        create_backup_frame = ttk_bs.LabelFrame(backup_frame, text="إنشاء نسخة احتياطية جديدة", padding=20)
        create_backup_frame.pack(fill=X, padx=10, pady=10)
        
        # نوع النسخة الاحتياطية
        backup_type_frame = ttk_bs.Frame(create_backup_frame)
        backup_type_frame.pack(fill=X, pady=10)
        
        ttk_bs.Label(backup_type_frame, text="نوع النسخة الاحتياطية:", font=("Arial", 12, "bold")).pack(anchor=W)
        
        self.backup_type_var = tk.StringVar(value="full")
        
        full_backup_radio = ttk_bs.Radiobutton(
            backup_type_frame,
            text="نسخة كاملة (قاعدة البيانات + الملفات)",
            variable=self.backup_type_var,
            value="full",
            bootstyle="success"
        )
        full_backup_radio.pack(anchor=W, pady=5)
        
        db_backup_radio = ttk_bs.Radiobutton(
            backup_type_frame,
            text="قاعدة البيانات فقط",
            variable=self.backup_type_var,
            value="database",
            bootstyle="info"
        )
        db_backup_radio.pack(anchor=W, pady=5)
        
        # مسار الحفظ
        path_frame = ttk_bs.Frame(create_backup_frame)
        path_frame.pack(fill=X, pady=10)
        
        ttk_bs.Label(path_frame, text="مسار الحفظ:").pack(anchor=W)
        
        path_entry_frame = ttk_bs.Frame(path_frame)
        path_entry_frame.pack(fill=X, pady=5)
        
        self.backup_path_var = tk.StringVar()
        backup_path_entry = ttk_bs.Entry(path_entry_frame, textvariable=self.backup_path_var, width=50)
        backup_path_entry.pack(side=LEFT, fill=X, expand=True)
        
        browse_btn = ttk_bs.Button(
            path_entry_frame,
            text="📁 تصفح",
            bootstyle="secondary-outline",
            command=self.browse_backup_path
        )
        browse_btn.pack(side=RIGHT, padx=(10, 0))
        
        # وصف النسخة الاحتياطية
        description_frame = ttk_bs.Frame(create_backup_frame)
        description_frame.pack(fill=X, pady=10)
        
        ttk_bs.Label(description_frame, text="وصف النسخة الاحتياطية:").pack(anchor=W)
        self.backup_description_text = tk.Text(description_frame, height=3, width=50)
        self.backup_description_text.pack(fill=X, pady=5)
        
        # أزرار العمليات
        backup_buttons_frame = ttk_bs.Frame(create_backup_frame)
        backup_buttons_frame.pack(pady=20)
        
        create_backup_btn = ttk_bs.Button(
            backup_buttons_frame,
            text="💾 إنشاء نسخة احتياطية",
            bootstyle="success",
            command=self.create_backup
        )
        create_backup_btn.pack(side=LEFT, padx=5)
        
        schedule_backup_btn = ttk_bs.Button(
            backup_buttons_frame,
            text="⏰ جدولة النسخ الاحتياطي",
            bootstyle="info-outline",
            command=self.schedule_backup
        )
        schedule_backup_btn.pack(side=LEFT, padx=5)
        
        # قسم سجل النسخ الاحتياطية
        history_frame = ttk_bs.LabelFrame(backup_frame, text="سجل النسخ الاحتياطية", padding=10)
        history_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # جدول سجل النسخ الاحتياطية
        columns = ("التاريخ", "النوع", "الحجم", "الوصف", "الحالة")
        self.backup_history_tree = ttk.Treeview(
            history_frame,
            columns=columns,
            show="headings",
            height=8
        )
        
        # تكوين الأعمدة
        column_widths = [150, 100, 100, 300, 100]
        for i, (col, width) in enumerate(zip(columns, column_widths)):
            self.backup_history_tree.heading(col, text=col)
            self.backup_history_tree.column(col, width=width, minwidth=50)
        
        # شريط التمرير
        backup_scrollbar = ttk.Scrollbar(
            history_frame,
            orient=VERTICAL,
            command=self.backup_history_tree.yview
        )
        self.backup_history_tree.configure(yscrollcommand=backup_scrollbar.set)
        
        # تخطيط العناصر
        self.backup_history_tree.pack(side=LEFT, fill=BOTH, expand=True)
        backup_scrollbar.pack(side=RIGHT, fill=Y)
        
    def create_restore_tab(self):
        """إنشاء قسم الاستعادة"""
        restore_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(restore_frame, text="🔄 الاستعادة")
        
        # قسم اختيار ملف الاستعادة
        select_file_frame = ttk_bs.LabelFrame(restore_frame, text="اختيار ملف الاستعادة", padding=20)
        select_file_frame.pack(fill=X, padx=10, pady=10)
        
        # مسار ملف الاستعادة
        restore_path_frame = ttk_bs.Frame(select_file_frame)
        restore_path_frame.pack(fill=X, pady=10)
        
        ttk_bs.Label(restore_path_frame, text="ملف النسخة الاحتياطية:").pack(anchor=W)
        
        restore_entry_frame = ttk_bs.Frame(restore_path_frame)
        restore_entry_frame.pack(fill=X, pady=5)
        
        self.restore_path_var = tk.StringVar()
        restore_path_entry = ttk_bs.Entry(restore_entry_frame, textvariable=self.restore_path_var, width=50)
        restore_path_entry.pack(side=LEFT, fill=X, expand=True)
        
        browse_restore_btn = ttk_bs.Button(
            restore_entry_frame,
            text="📁 تصفح",
            bootstyle="secondary-outline",
            command=self.browse_restore_file
        )
        browse_restore_btn.pack(side=RIGHT, padx=(10, 0))
        
        # معلومات النسخة الاحتياطية
        info_frame = ttk_bs.LabelFrame(select_file_frame, text="معلومات النسخة الاحتياطية", padding=10)
        info_frame.pack(fill=X, pady=10)
        
        self.backup_info_text = tk.Text(info_frame, height=5, width=50, state=tk.DISABLED)
        self.backup_info_text.pack(fill=X)
        
        # خيارات الاستعادة
        options_frame = ttk_bs.LabelFrame(select_file_frame, text="خيارات الاستعادة", padding=10)
        options_frame.pack(fill=X, pady=10)
        
        self.restore_database_var = tk.BooleanVar(value=True)
        self.restore_files_var = tk.BooleanVar(value=True)
        self.create_backup_before_restore_var = tk.BooleanVar(value=True)
        
        restore_db_check = ttk_bs.Checkbutton(
            options_frame,
            text="استعادة قاعدة البيانات",
            variable=self.restore_database_var,
            bootstyle="success"
        )
        restore_db_check.pack(anchor=W, pady=2)

        restore_files_check = ttk_bs.Checkbutton(
            options_frame,
            text="استعادة الملفات",
            variable=self.restore_files_var,
            bootstyle="success"
        )
        restore_files_check.pack(anchor=W, pady=2)

        backup_before_restore_check = ttk_bs.Checkbutton(
            options_frame,
            text="إنشاء نسخة احتياطية قبل الاستعادة",
            variable=self.create_backup_before_restore_var,
            bootstyle="warning"
        )
        backup_before_restore_check.pack(anchor=W, pady=2)
        
        # أزرار الاستعادة
        restore_buttons_frame = ttk_bs.Frame(select_file_frame)
        restore_buttons_frame.pack(pady=20)
        
        analyze_btn = ttk_bs.Button(
            restore_buttons_frame,
            text="🔍 تحليل النسخة الاحتياطية",
            bootstyle="info",
            command=self.analyze_backup_file
        )
        analyze_btn.pack(side=LEFT, padx=5)
        
        restore_btn = ttk_bs.Button(
            restore_buttons_frame,
            text="🔄 بدء الاستعادة",
            bootstyle="warning",
            command=self.start_restore
        )
        restore_btn.pack(side=LEFT, padx=5)
        
    def create_settings_tab(self):
        """إنشاء قسم الإعدادات"""
        settings_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(settings_frame, text="⚙️ الإعدادات")
        
        # إعدادات النسخ الاحتياطي التلقائي
        auto_backup_frame = ttk_bs.LabelFrame(settings_frame, text="النسخ الاحتياطي التلقائي", padding=20)
        auto_backup_frame.pack(fill=X, padx=10, pady=10)
        
        self.auto_backup_enabled_var = tk.BooleanVar()
        auto_backup_check = ttk_bs.Checkbutton(
            auto_backup_frame,
            text="تفعيل النسخ الاحتياطي التلقائي",
            variable=self.auto_backup_enabled_var,
            bootstyle="success"
        )
        auto_backup_check.pack(anchor=W, pady=10)
        
        # تكرار النسخ الاحتياطي
        frequency_frame = ttk_bs.Frame(auto_backup_frame)
        frequency_frame.pack(fill=X, pady=10)
        
        ttk_bs.Label(frequency_frame, text="تكرار النسخ الاحتياطي:").pack(anchor=W)
        
        self.backup_frequency_var = tk.StringVar(value="daily")
        frequency_combo = ttk_bs.Combobox(
            frequency_frame,
            textvariable=self.backup_frequency_var,
            values=["يومي", "أسبوعي", "شهري"],
            state="readonly",
            width=20
        )
        frequency_combo.pack(anchor=W, pady=5)
        
        # مسار النسخ الاحتياطي الافتراضي
        default_path_frame = ttk_bs.Frame(auto_backup_frame)
        default_path_frame.pack(fill=X, pady=10)
        
        ttk_bs.Label(default_path_frame, text="مسار النسخ الاحتياطي الافتراضي:").pack(anchor=W)
        
        default_path_entry_frame = ttk_bs.Frame(default_path_frame)
        default_path_entry_frame.pack(fill=X, pady=5)
        
        self.default_backup_path_var = tk.StringVar()
        default_path_entry = ttk_bs.Entry(default_path_entry_frame, textvariable=self.default_backup_path_var, width=50)
        default_path_entry.pack(side=LEFT, fill=X, expand=True)
        
        browse_default_btn = ttk_bs.Button(
            default_path_entry_frame,
            text="📁 تصفح",
            bootstyle="secondary-outline",
            command=self.browse_default_path
        )
        browse_default_btn.pack(side=RIGHT, padx=(10, 0))
        
        # أزرار حفظ الإعدادات
        settings_buttons_frame = ttk_bs.Frame(auto_backup_frame)
        settings_buttons_frame.pack(pady=20)
        
        save_settings_btn = ttk_bs.Button(
            settings_buttons_frame,
            text="💾 حفظ الإعدادات",
            bootstyle="success",
            command=self.save_settings
        )
        save_settings_btn.pack(side=LEFT, padx=5)
        
        reset_settings_btn = ttk_bs.Button(
            settings_buttons_frame,
            text="🔄 إعادة تعيين",
            bootstyle="secondary",
            command=self.reset_settings
        )
        reset_settings_btn.pack(side=LEFT, padx=5)

    def browse_backup_path(self):
        """تصفح مسار حفظ النسخة الاحتياطية"""
        folder_path = filedialog.askdirectory(title="اختر مجلد حفظ النسخة الاحتياطية")
        if folder_path:
            self.backup_path_var.set(folder_path)

    def browse_restore_file(self):
        """تصفح ملف الاستعادة"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف النسخة الاحتياطية",
            filetypes=[("Backup files", "*.backup"), ("All files", "*.*")]
        )
        if file_path:
            self.restore_path_var.set(file_path)

    def browse_default_path(self):
        """تصفح المسار الافتراضي للنسخ الاحتياطي"""
        folder_path = filedialog.askdirectory(title="اختر المجلد الافتراضي للنسخ الاحتياطي")
        if folder_path:
            self.default_backup_path_var.set(folder_path)

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        backup_path = self.backup_path_var.get().strip()
        if not backup_path:
            messagebox.showerror("خطأ", "يرجى اختيار مسار حفظ النسخة الاحتياطية")
            return

        if not os.path.exists(backup_path):
            messagebox.showerror("خطأ", "المسار المحدد غير موجود")
            return

        try:
            # إنشاء اسم ملف النسخة الاحتياطية
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_type = self.backup_type_var.get()
            backup_filename = f"supply_backup_{backup_type}_{timestamp}.backup"
            backup_file_path = os.path.join(backup_path, backup_filename)

            # إنشاء النسخة الاحتياطية
            backup_data = {
                "timestamp": datetime.now().isoformat(),
                "type": backup_type,
                "description": self.backup_description_text.get("1.0", tk.END).strip(),
                "version": "1.0"
            }

            if backup_type == "full" or backup_type == "database":
                # نسخ قاعدة البيانات
                db_backup = self.backup_database()
                backup_data["database"] = db_backup

            if backup_type == "full":
                # نسخ الملفات (إذا كانت موجودة)
                files_backup = self.backup_files()
                backup_data["files"] = files_backup

            # حفظ النسخة الاحتياطية
            with open(backup_file_path, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, ensure_ascii=False, indent=2)

            # حساب حجم الملف
            file_size = os.path.getsize(backup_file_path)
            file_size_mb = file_size / (1024 * 1024)

            # تسجيل النسخة الاحتياطية في السجل
            self.log_backup(backup_filename, backup_type, file_size_mb, backup_data["description"])

            messagebox.showinfo("نجح", f"تم إنشاء النسخة الاحتياطية بنجاح\nالملف: {backup_filename}\nالحجم: {file_size_mb:.2f} ميجابايت")

            # تحديث سجل النسخ الاحتياطية
            self.load_backup_history()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}")

    def backup_database(self):
        """نسخ قاعدة البيانات"""
        try:
            # الحصول على جميع الجداول والبيانات
            tables_data = {}

            # قائمة الجداول
            tables = [
                'users', 'military_units', 'equipment', 'items', 'suppliers',
                'stock_movements', 'supply_requests', 'request_items', 'activity_log'
            ]

            for table in tables:
                try:
                    data = self.db_manager.fetch_all(f"SELECT * FROM {table}")
                    tables_data[table] = data
                except:
                    tables_data[table] = []

            return tables_data

        except Exception as e:
            raise Exception(f"خطأ في نسخ قاعدة البيانات: {str(e)}")

    def backup_files(self):
        """نسخ الملفات"""
        # في هذا التطبيق، لا توجد ملفات إضافية للنسخ
        # يمكن إضافة منطق نسخ الملفات هنا إذا لزم الأمر
        return {}

    def log_backup(self, filename, backup_type, file_size, description):
        """تسجيل النسخة الاحتياطية في السجل"""
        try:
            query = """
            INSERT INTO backup_log (backup_date, backup_type, filename, file_size, description, status)
            VALUES (?, ?, ?, ?, ?, ?)
            """
            params = (
                datetime.now().isoformat(),
                backup_type,
                filename,
                file_size,
                description,
                "مكتملة"
            )

            # إنشاء جدول سجل النسخ الاحتياطية إذا لم يكن موجوداً
            self.create_backup_log_table()
            self.db_manager.execute_query(query, params)

        except Exception as e:
            print(f"خطأ في تسجيل النسخة الاحتياطية: {str(e)}")

    def create_backup_log_table(self):
        """إنشاء جدول سجل النسخ الاحتياطية"""
        try:
            query = """
            CREATE TABLE IF NOT EXISTS backup_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                backup_date TEXT NOT NULL,
                backup_type TEXT NOT NULL,
                filename TEXT NOT NULL,
                file_size REAL,
                description TEXT,
                status TEXT DEFAULT 'مكتملة',
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
            """
            self.db_manager.execute_query(query)
        except:
            pass

    def load_backup_history(self):
        """تحميل سجل النسخ الاحتياطية"""
        try:
            # إنشاء الجدول إذا لم يكن موجوداً
            self.create_backup_log_table()

            # مسح البيانات الحالية
            for item in self.backup_history_tree.get_children():
                self.backup_history_tree.delete(item)

            # جلب البيانات
            backups = self.db_manager.fetch_all(
                "SELECT * FROM backup_log ORDER BY backup_date DESC"
            )

            # إضافة البيانات إلى الجدول
            for backup in backups:
                backup_date = datetime.fromisoformat(backup['backup_date']).strftime("%Y-%m-%d %H:%M")
                file_size = f"{backup['file_size']:.2f} MB" if backup['file_size'] else "غير محدد"

                self.backup_history_tree.insert("", "end", values=(
                    backup_date,
                    backup['backup_type'],
                    file_size,
                    backup['description'] or "",
                    backup['status']
                ))

        except Exception as e:
            print(f"خطأ في تحميل سجل النسخ الاحتياطية: {str(e)}")

    def analyze_backup_file(self):
        """تحليل ملف النسخة الاحتياطية"""
        restore_path = self.restore_path_var.get().strip()
        if not restore_path:
            messagebox.showerror("خطأ", "يرجى اختيار ملف النسخة الاحتياطية")
            return

        if not os.path.exists(restore_path):
            messagebox.showerror("خطأ", "الملف المحدد غير موجود")
            return

        try:
            with open(restore_path, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)

            # عرض معلومات النسخة الاحتياطية
            info_text = f"""تاريخ النسخة الاحتياطية: {backup_data.get('timestamp', 'غير محدد')}
نوع النسخة: {backup_data.get('type', 'غير محدد')}
الإصدار: {backup_data.get('version', 'غير محدد')}
الوصف: {backup_data.get('description', 'لا يوجد وصف')}

محتويات النسخة الاحتياطية:"""

            if 'database' in backup_data:
                info_text += "\n- قاعدة البيانات"
                for table, data in backup_data['database'].items():
                    info_text += f"\n  • {table}: {len(data)} سجل"

            if 'files' in backup_data:
                info_text += "\n- الملفات"

            # عرض المعلومات
            self.backup_info_text.config(state=tk.NORMAL)
            self.backup_info_text.delete("1.0", tk.END)
            self.backup_info_text.insert("1.0", info_text)
            self.backup_info_text.config(state=tk.DISABLED)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحليل ملف النسخة الاحتياطية: {str(e)}")

    def start_restore(self):
        """بدء عملية الاستعادة"""
        restore_path = self.restore_path_var.get().strip()
        if not restore_path:
            messagebox.showerror("خطأ", "يرجى اختيار ملف النسخة الاحتياطية")
            return

        if not os.path.exists(restore_path):
            messagebox.showerror("خطأ", "الملف المحدد غير موجود")
            return

        # تأكيد الاستعادة
        if not messagebox.askyesno("تأكيد الاستعادة",
                                  "هل أنت متأكد من بدء عملية الاستعادة؟\nسيتم استبدال البيانات الحالية."):
            return

        try:
            # إنشاء نسخة احتياطية قبل الاستعادة إذا كان مطلوباً
            if self.create_backup_before_restore_var.get():
                self.create_emergency_backup()

            # قراءة ملف النسخة الاحتياطية
            with open(restore_path, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)

            # استعادة قاعدة البيانات
            if self.restore_database_var.get() and 'database' in backup_data:
                self.restore_database(backup_data['database'])

            # استعادة الملفات
            if self.restore_files_var.get() and 'files' in backup_data:
                self.restore_files(backup_data['files'])

            messagebox.showinfo("نجح", "تم استعادة النسخة الاحتياطية بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في استعادة النسخة الاحتياطية: {str(e)}")

    def create_emergency_backup(self):
        """إنشاء نسخة احتياطية طارئة قبل الاستعادة"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            emergency_backup_path = f"emergency_backup_{timestamp}.backup"

            backup_data = {
                "timestamp": datetime.now().isoformat(),
                "type": "emergency",
                "description": "نسخة احتياطية طارئة قبل الاستعادة",
                "version": "1.0",
                "database": self.backup_database()
            }

            with open(emergency_backup_path, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            raise Exception(f"خطأ في إنشاء النسخة الاحتياطية الطارئة: {str(e)}")

    def restore_database(self, database_data):
        """استعادة قاعدة البيانات"""
        try:
            for table, data in database_data.items():
                if not data:
                    continue

                # مسح البيانات الحالية
                self.db_manager.execute_query(f"DELETE FROM {table}")

                # إدراج البيانات المستعادة
                if data:
                    columns = list(data[0].keys())
                    placeholders = ', '.join(['?' for _ in columns])
                    query = f"INSERT INTO {table} ({', '.join(columns)}) VALUES ({placeholders})"

                    for row in data:
                        values = [row[col] for col in columns]
                        self.db_manager.execute_query(query, values)

        except Exception as e:
            raise Exception(f"خطأ في استعادة قاعدة البيانات: {str(e)}")

    def restore_files(self, files_data):
        """استعادة الملفات"""
        # في هذا التطبيق، لا توجد ملفات إضافية للاستعادة
        pass

    def schedule_backup(self):
        """جدولة النسخ الاحتياطي"""
        messagebox.showinfo("قريباً", "ميزة جدولة النسخ الاحتياطي ستكون متاحة قريباً")

    def save_settings(self):
        """حفظ إعدادات النسخ الاحتياطي"""
        messagebox.showinfo("نجح", "تم حفظ الإعدادات بنجاح")

    def reset_settings(self):
        """إعادة تعيين الإعدادات"""
        self.auto_backup_enabled_var.set(False)
        self.backup_frequency_var.set("daily")
        self.default_backup_path_var.set("")
        messagebox.showinfo("نجح", "تم إعادة تعيين الإعدادات")
