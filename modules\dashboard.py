#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة لوحة التحكم
Dashboard Module for Supply Management System
"""

import tkinter as tk
from tkinter import ttk
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import numpy as np

class Dashboard:
    """لوحة التحكم الرئيسية"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        
        # إنشاء الإطار الرئيسي
        self.frame = ttk_bs.Frame(parent)
        
        # إعداد واجهة لوحة التحكم
        self.setup_dashboard()
        
        # تحديث البيانات
        self.refresh_data()
        
    def setup_dashboard(self):
        """إعداد واجهة لوحة التحكم"""
        
        # العنوان الرئيسي
        title_frame = ttk_bs.Frame(self.frame)
        title_frame.pack(fill=X, padx=10, pady=10)
        
        title_label = ttk_bs.Label(
            title_frame,
            text="🏠 لوحة التحكم الرئيسية",
            font=("Arial", 18, "bold"),
            bootstyle="primary"
        )
        title_label.pack(side=LEFT)
        
        # زر التحديث
        refresh_btn = ttk_bs.Button(
            title_frame,
            text="🔄 تحديث",
            bootstyle="success-outline",
            command=self.refresh_data
        )
        refresh_btn.pack(side=RIGHT)
        
        # إنشاء الشبكة الرئيسية
        main_grid = ttk_bs.Frame(self.frame)
        main_grid.pack(fill=BOTH, expand=True, padx=10, pady=5)
        
        # الصف الأول - بطاقات الإحصائيات
        self.create_stats_cards(main_grid)
        
        # الصف الثاني - الرسوم البيانية والجداول
        self.create_charts_section(main_grid)
        
        # الصف الثالث - النشاطات الأخيرة والتنبيهات
        self.create_activities_section(main_grid)
        
    def create_stats_cards(self, parent):
        """إنشاء بطاقات الإحصائيات"""
        stats_frame = ttk_bs.Frame(parent)
        stats_frame.pack(fill=X, pady=10)
        
        # بطاقة إجمالي المعدات
        self.equipment_card = self.create_stat_card(
            stats_frame,
            "⚙️ إجمالي المعدات",
            "0",
            "primary",
            0
        )
        
        # بطاقة إجمالي الأصناف
        self.items_card = self.create_stat_card(
            stats_frame,
            "📦 إجمالي الأصناف",
            "0",
            "info",
            1
        )
        
        # بطاقة الوحدات العسكرية
        self.units_card = self.create_stat_card(
            stats_frame,
            "🏢 الوحدات العسكرية",
            "0",
            "success",
            2
        )
        
        # بطاقة طلبات التوريد المعلقة
        self.pending_requests_card = self.create_stat_card(
            stats_frame,
            "⏳ طلبات معلقة",
            "0",
            "warning",
            3
        )
        
        # بطاقة الأصناف منخفضة المخزون
        self.low_stock_card = self.create_stat_card(
            stats_frame,
            "⚠️ مخزون منخفض",
            "0",
            "danger",
            4
        )
        
    def create_stat_card(self, parent, title, value, style, column):
        """إنشاء بطاقة إحصائية"""
        card_frame = ttk_bs.LabelFrame(
            parent,
            text=title,
            bootstyle=style,
            padding=15
        )
        card_frame.grid(row=0, column=column, padx=5, pady=5, sticky="ew")
        
        # قيمة الإحصائية
        value_label = ttk_bs.Label(
            card_frame,
            text=value,
            font=("Arial", 24, "bold"),
            bootstyle=style
        )
        value_label.pack()
        
        # تكوين الأعمدة
        parent.grid_columnconfigure(column, weight=1)
        
        return value_label
        
    def create_charts_section(self, parent):
        """إنشاء قسم الرسوم البيانية"""
        charts_frame = ttk_bs.Frame(parent)
        charts_frame.pack(fill=BOTH, expand=True, pady=10)
        
        # الإطار الأيسر - رسم بياني للمخزون
        left_frame = ttk_bs.LabelFrame(
            charts_frame,
            text="📊 حالة المخزون",
            padding=10
        )
        left_frame.pack(side=LEFT, fill=BOTH, expand=True, padx=(0, 5))
        
        self.create_stock_chart(left_frame)
        
        # الإطار الأيمن - رسم بياني لطلبات التوريد
        right_frame = ttk_bs.LabelFrame(
            charts_frame,
            text="📈 طلبات التوريد الشهرية",
            padding=10
        )
        right_frame.pack(side=RIGHT, fill=BOTH, expand=True, padx=(5, 0))
        
        self.create_requests_chart(right_frame)
        
    def create_stock_chart(self, parent):
        """إنشاء رسم بياني للمخزون"""
        # إنشاء الرسم البياني
        fig, ax = plt.subplots(figsize=(6, 4))
        fig.patch.set_facecolor('white')
        
        # بيانات وهمية للمخزون
        categories = ['أسلحة', 'ذخيرة', 'معدات', 'مواد غذائية', 'وقود']
        values = [85, 92, 78, 95, 88]
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
        
        bars = ax.bar(categories, values, color=colors)
        ax.set_ylabel('نسبة المخزون %')
        ax.set_title('حالة المخزون حسب الفئة')
        ax.set_ylim(0, 100)
        
        # إضافة قيم على الأعمدة
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                   f'{value}%', ha='center', va='bottom')
        
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        # إدراج الرسم في واجهة tkinter
        canvas = FigureCanvasTkAgg(fig, parent)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=BOTH, expand=True)
        
    def create_requests_chart(self, parent):
        """إنشاء رسم بياني لطلبات التوريد"""
        # إنشاء الرسم البياني
        fig, ax = plt.subplots(figsize=(6, 4))
        fig.patch.set_facecolor('white')
        
        # بيانات وهمية لطلبات التوريد
        months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو']
        requests = [45, 52, 38, 61, 48, 55]
        
        ax.plot(months, requests, marker='o', linewidth=2, markersize=8, color='#45B7D1')
        ax.fill_between(months, requests, alpha=0.3, color='#45B7D1')
        ax.set_ylabel('عدد الطلبات')
        ax.set_title('طلبات التوريد الشهرية')
        ax.grid(True, alpha=0.3)
        
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        # إدراج الرسم في واجهة tkinter
        canvas = FigureCanvasTkAgg(fig, parent)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=BOTH, expand=True)
        
    def create_activities_section(self, parent):
        """إنشاء قسم النشاطات والتنبيهات"""
        activities_frame = ttk_bs.Frame(parent)
        activities_frame.pack(fill=BOTH, expand=True, pady=10)
        
        # النشاطات الأخيرة
        activities_left = ttk_bs.LabelFrame(
            activities_frame,
            text="📋 النشاطات الأخيرة",
            padding=10
        )
        activities_left.pack(side=LEFT, fill=BOTH, expand=True, padx=(0, 5))
        
        self.create_activities_list(activities_left)
        
        # التنبيهات والإشعارات
        alerts_right = ttk_bs.LabelFrame(
            activities_frame,
            text="🔔 التنبيهات والإشعارات",
            padding=10
        )
        alerts_right.pack(side=RIGHT, fill=BOTH, expand=True, padx=(5, 0))
        
        self.create_alerts_list(alerts_right)
        
    def create_activities_list(self, parent):
        """إنشاء قائمة النشاطات الأخيرة"""
        # إنشاء Treeview للنشاطات
        columns = ("الوقت", "النشاط", "المستخدم")
        self.activities_tree = ttk.Treeview(
            parent,
            columns=columns,
            show="headings",
            height=8
        )
        
        # تكوين الأعمدة
        for col in columns:
            self.activities_tree.heading(col, text=col)
            self.activities_tree.column(col, width=120)
        
        # شريط التمرير
        activities_scrollbar = ttk.Scrollbar(
            parent,
            orient=VERTICAL,
            command=self.activities_tree.yview
        )
        self.activities_tree.configure(yscrollcommand=activities_scrollbar.set)
        
        # تخطيط العناصر
        self.activities_tree.pack(side=LEFT, fill=BOTH, expand=True)
        activities_scrollbar.pack(side=RIGHT, fill=Y)
        
    def create_alerts_list(self, parent):
        """إنشاء قائمة التنبيهات"""
        # إطار للتنبيهات
        alerts_frame = ttk_bs.Frame(parent)
        alerts_frame.pack(fill=BOTH, expand=True)
        
        # تنبيهات وهمية
        alerts = [
            ("⚠️", "مخزون الذخيرة منخفض", "danger"),
            ("🔄", "طلب توريد جديد", "info"),
            ("✅", "تم استلام معدات جديدة", "success"),
            ("⏰", "انتهاء صلاحية مواد غذائية", "warning"),
            ("🔧", "صيانة معدات مطلوبة", "secondary")
        ]
        
        for icon, message, style in alerts:
            alert_frame = ttk_bs.Frame(alerts_frame)
            alert_frame.pack(fill=X, pady=2)
            
            icon_label = ttk_bs.Label(
                alert_frame,
                text=icon,
                font=("Arial", 12)
            )
            icon_label.pack(side=LEFT, padx=(0, 10))
            
            message_label = ttk_bs.Label(
                alert_frame,
                text=message,
                bootstyle=style
            )
            message_label.pack(side=LEFT)
            
    def refresh_data(self):
        """تحديث بيانات لوحة التحكم"""
        try:
            # تحديث إحصائيات المعدات
            equipment_count = self.get_equipment_count()
            self.equipment_card.config(text=str(equipment_count))
            
            # تحديث إحصائيات الأصناف
            items_count = self.get_items_count()
            self.items_card.config(text=str(items_count))
            
            # تحديث إحصائيات الوحدات
            units_count = self.get_units_count()
            self.units_card.config(text=str(units_count))
            
            # تحديث الطلبات المعلقة
            pending_requests = self.get_pending_requests_count()
            self.pending_requests_card.config(text=str(pending_requests))
            
            # تحديث المخزون المنخفض
            low_stock_count = self.get_low_stock_count()
            self.low_stock_card.config(text=str(low_stock_count))
            
            # تحديث النشاطات الأخيرة
            self.update_activities()
            
        except Exception as e:
            print(f"خطأ في تحديث البيانات: {e}")
            
    def get_equipment_count(self):
        """الحصول على عدد المعدات"""
        result = self.db_manager.fetch_one("SELECT COUNT(*) as count FROM equipment")
        return result['count'] if result else 0
        
    def get_items_count(self):
        """الحصول على عدد الأصناف"""
        result = self.db_manager.fetch_one("SELECT COUNT(*) as count FROM items")
        return result['count'] if result else 0
        
    def get_units_count(self):
        """الحصول على عدد الوحدات العسكرية"""
        result = self.db_manager.fetch_one("SELECT COUNT(*) as count FROM military_units")
        return result['count'] if result else 0
        
    def get_pending_requests_count(self):
        """الحصول على عدد الطلبات المعلقة"""
        result = self.db_manager.fetch_one(
            "SELECT COUNT(*) as count FROM supply_requests WHERE status = 'pending'"
        )
        return result['count'] if result else 0
        
    def get_low_stock_count(self):
        """الحصول على عدد الأصناف منخفضة المخزون"""
        result = self.db_manager.fetch_one(
            "SELECT COUNT(*) as count FROM items WHERE current_stock <= minimum_stock"
        )
        return result['count'] if result else 0
        
    def update_activities(self):
        """تحديث قائمة النشاطات الأخيرة"""
        # مسح البيانات الحالية
        for item in self.activities_tree.get_children():
            self.activities_tree.delete(item)
            
        # جلب النشاطات الأخيرة من قاعدة البيانات
        activities = self.db_manager.fetch_all("""
            SELECT al.created_at, al.action, u.full_name
            FROM activity_log al
            LEFT JOIN users u ON al.user_id = u.id
            ORDER BY al.created_at DESC
            LIMIT 10
        """)
        
        # إضافة النشاطات إلى القائمة
        for activity in activities:
            time_str = activity['created_at'][:16] if activity['created_at'] else ""
            action = activity['action'] or ""
            user = activity['full_name'] or "غير محدد"
            
            self.activities_tree.insert("", "end", values=(time_str, action, user))
