#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة إدارة المستخدمين
User Management Module for Supply Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime
import hashlib

class UserManagement:
    """وحدة إدارة المستخدمين"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        
        # إنشاء الإطار الرئيسي
        self.frame = ttk_bs.Frame(parent)
        
        # متغيرات النموذج
        self.setup_variables()
        
        # إعداد واجهة المستخدم
        self.setup_ui()
        
        # تحميل البيانات
        self.load_data()
        
    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        self.username_var = tk.StringVar()
        self.password_var = tk.StringVar()
        self.confirm_password_var = tk.StringVar()
        self.full_name_var = tk.StringVar()
        self.email_var = tk.StringVar()
        self.role_var = tk.StringVar()
        self.is_active_var = tk.BooleanVar()
        self.selected_user_id = None
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # العنوان الرئيسي
        title_frame = ttk_bs.Frame(self.frame)
        title_frame.pack(fill=X, padx=10, pady=10)
        
        title_label = ttk_bs.Label(
            title_frame,
            text="🔐 إدارة المستخدمين والصلاحيات",
            font=("Arial", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack(side=LEFT)
        
        # أزرار سريعة
        quick_buttons_frame = ttk_bs.Frame(title_frame)
        quick_buttons_frame.pack(side=RIGHT)
        
        permissions_btn = ttk_bs.Button(
            quick_buttons_frame,
            text="🔑 الصلاحيات",
            bootstyle="info-outline",
            command=self.manage_permissions
        )
        permissions_btn.pack(side=LEFT, padx=5)
        
        activity_btn = ttk_bs.Button(
            quick_buttons_frame,
            text="📊 سجل النشاط",
            bootstyle="secondary-outline",
            command=self.show_activity_log
        )
        activity_btn.pack(side=LEFT, padx=5)
        
        # تقسيم الشاشة
        main_paned = ttk_bs.PanedWindow(self.frame, orient=HORIZONTAL)
        main_paned.pack(fill=BOTH, expand=True, padx=10, pady=5)
        
        # الجانب الأيسر - نموذج الإدخال
        left_frame = ttk_bs.LabelFrame(main_paned, text="بيانات المستخدم", padding=10)
        main_paned.add(left_frame, weight=1)
        
        self.create_user_form(left_frame)
        
        # الجانب الأيمن - قائمة المستخدمين
        right_frame = ttk_bs.LabelFrame(main_paned, text="قائمة المستخدمين", padding=10)
        main_paned.add(right_frame, weight=2)
        
        self.create_users_list(right_frame)
        
    def create_user_form(self, parent):
        """إنشاء نموذج بيانات المستخدم"""
        
        # اسم المستخدم
        ttk_bs.Label(parent, text="اسم المستخدم:").grid(row=0, column=0, sticky=W, pady=5)
        username_entry = ttk_bs.Entry(parent, textvariable=self.username_var, width=30)
        username_entry.grid(row=0, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # كلمة المرور
        ttk_bs.Label(parent, text="كلمة المرور:").grid(row=1, column=0, sticky=W, pady=5)
        password_entry = ttk_bs.Entry(parent, textvariable=self.password_var, width=30, show="*")
        password_entry.grid(row=1, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # تأكيد كلمة المرور
        ttk_bs.Label(parent, text="تأكيد كلمة المرور:").grid(row=2, column=0, sticky=W, pady=5)
        confirm_password_entry = ttk_bs.Entry(parent, textvariable=self.confirm_password_var, width=30, show="*")
        confirm_password_entry.grid(row=2, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # الاسم الكامل
        ttk_bs.Label(parent, text="الاسم الكامل:").grid(row=3, column=0, sticky=W, pady=5)
        full_name_entry = ttk_bs.Entry(parent, textvariable=self.full_name_var, width=30)
        full_name_entry.grid(row=3, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # البريد الإلكتروني
        ttk_bs.Label(parent, text="البريد الإلكتروني:").grid(row=4, column=0, sticky=W, pady=5)
        email_entry = ttk_bs.Entry(parent, textvariable=self.email_var, width=30)
        email_entry.grid(row=4, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # الدور
        ttk_bs.Label(parent, text="الدور:").grid(row=5, column=0, sticky=W, pady=5)
        role_combo = ttk_bs.Combobox(
            parent,
            textvariable=self.role_var,
            values=["admin", "manager", "operator", "viewer"],
            width=27,
            state="readonly"
        )
        role_combo.grid(row=5, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # حالة النشاط
        active_check = ttk_bs.Checkbutton(
            parent,
            text="مستخدم نشط",
            variable=self.is_active_var,
            bootstyle="success"
        )
        active_check.grid(row=6, column=0, columnspan=2, sticky=W, pady=10)
        
        # أزرار العمليات
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.grid(row=7, column=0, columnspan=2, pady=20)
        
        # زر إضافة
        add_btn = ttk_bs.Button(
            buttons_frame,
            text="➕ إضافة",
            bootstyle="success",
            command=self.add_user
        )
        add_btn.pack(side=LEFT, padx=5)
        
        # زر تحديث
        update_btn = ttk_bs.Button(
            buttons_frame,
            text="✏️ تحديث",
            bootstyle="warning",
            command=self.update_user
        )
        update_btn.pack(side=LEFT, padx=5)
        
        # زر حذف
        delete_btn = ttk_bs.Button(
            buttons_frame,
            text="🗑️ حذف",
            bootstyle="danger",
            command=self.delete_user
        )
        delete_btn.pack(side=LEFT, padx=5)
        
        # زر مسح النموذج
        clear_btn = ttk_bs.Button(
            buttons_frame,
            text="🧹 مسح",
            bootstyle="secondary",
            command=self.clear_form
        )
        clear_btn.pack(side=LEFT, padx=5)
        
        # زر تغيير كلمة المرور
        change_password_btn = ttk_bs.Button(
            buttons_frame,
            text="🔑 تغيير كلمة المرور",
            bootstyle="info",
            command=self.change_password
        )
        change_password_btn.pack(side=LEFT, padx=5)
        
        # تكوين الأعمدة
        parent.grid_columnconfigure(1, weight=1)
        
    def create_users_list(self, parent):
        """إنشاء قائمة المستخدمين"""
        
        # إطار البحث
        search_frame = ttk_bs.Frame(parent)
        search_frame.pack(fill=X, pady=(0, 10))
        
        ttk_bs.Label(search_frame, text="البحث:").pack(side=LEFT)
        self.search_var = tk.StringVar()
        search_entry = ttk_bs.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side=LEFT, padx=(10, 5))
        search_entry.bind('<KeyRelease>', self.search_users)
        
        search_btn = ttk_bs.Button(
            search_frame,
            text="🔍",
            bootstyle="info-outline",
            command=self.search_users
        )
        search_btn.pack(side=LEFT)
        
        # جدول المستخدمين
        columns = ("الرقم", "اسم المستخدم", "الاسم الكامل", "البريد الإلكتروني", "الدور", "الحالة", "آخر دخول")
        self.users_tree = ttk.Treeview(
            parent,
            columns=columns,
            show="headings",
            height=15
        )
        
        # تكوين الأعمدة
        column_widths = [60, 120, 200, 200, 100, 80, 150]
        for i, (col, width) in enumerate(zip(columns, column_widths)):
            self.users_tree.heading(col, text=col)
            self.users_tree.column(col, width=width, minwidth=50)
        
        # تلوين الصفوف حسب الحالة
        self.users_tree.tag_configure('active', background='#ccffcc')
        self.users_tree.tag_configure('inactive', background='#ffcccc')
        
        # شريط التمرير
        users_scrollbar = ttk.Scrollbar(
            parent,
            orient=VERTICAL,
            command=self.users_tree.yview
        )
        self.users_tree.configure(yscrollcommand=users_scrollbar.set)
        
        # ربط الأحداث
        self.users_tree.bind('<Double-1>', self.on_user_select)
        
        # تخطيط العناصر
        self.users_tree.pack(side=LEFT, fill=BOTH, expand=True)
        users_scrollbar.pack(side=RIGHT, fill=Y)
        
    def add_user(self):
        """إضافة مستخدم جديد"""
        if not self.validate_user_form():
            return
            
        try:
            # تشفير كلمة المرور
            password_hash = self.hash_password(self.password_var.get())
            
            query = """
            INSERT INTO users (username, password_hash, full_name, email, role, is_active)
            VALUES (?, ?, ?, ?, ?, ?)
            """
            
            params = (
                self.username_var.get(),
                password_hash,
                self.full_name_var.get(),
                self.email_var.get(),
                self.role_var.get(),
                self.is_active_var.get()
            )
            
            self.db_manager.execute_query(query, params)
            
            messagebox.showinfo("نجح", "تم إضافة المستخدم بنجاح")
            self.clear_form()
            self.load_users()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إضافة المستخدم: {str(e)}")
            
    def update_user(self):
        """تحديث بيانات المستخدم"""
        if not self.selected_user_id:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم للتحديث")
            return
            
        if not self.validate_user_form(update_mode=True):
            return
            
        try:
            query = """
            UPDATE users 
            SET username=?, full_name=?, email=?, role=?, is_active=?
            WHERE id=?
            """
            
            params = (
                self.username_var.get(),
                self.full_name_var.get(),
                self.email_var.get(),
                self.role_var.get(),
                self.is_active_var.get(),
                self.selected_user_id
            )
            
            self.db_manager.execute_query(query, params)
            
            messagebox.showinfo("نجح", "تم تحديث بيانات المستخدم بنجاح")
            self.clear_form()
            self.load_users()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحديث المستخدم: {str(e)}")
            
    def delete_user(self):
        """حذف المستخدم"""
        if not self.selected_user_id:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم للحذف")
            return
            
        # التحقق من عدم حذف المدير الرئيسي
        user = self.db_manager.fetch_one("SELECT username FROM users WHERE id=?", (self.selected_user_id,))
        if user and user['username'] == 'admin':
            messagebox.showerror("خطأ", "لا يمكن حذف المدير الرئيسي")
            return
            
        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذا المستخدم؟"):
            try:
                query = "DELETE FROM users WHERE id=?"
                self.db_manager.execute_query(query, (self.selected_user_id,))
                
                messagebox.showinfo("نجح", "تم حذف المستخدم بنجاح")
                self.clear_form()
                self.load_users()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في حذف المستخدم: {str(e)}")
                
    def validate_user_form(self, update_mode=False):
        """التحقق من صحة بيانات النموذج"""
        if not self.username_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم")
            return False
            
        if not update_mode:
            if not self.password_var.get():
                messagebox.showerror("خطأ", "يرجى إدخال كلمة المرور")
                return False
                
            if self.password_var.get() != self.confirm_password_var.get():
                messagebox.showerror("خطأ", "كلمة المرور وتأكيدها غير متطابقتين")
                return False
                
        if not self.full_name_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال الاسم الكامل")
            return False
            
        if not self.role_var.get():
            messagebox.showerror("خطأ", "يرجى اختيار دور المستخدم")
            return False
            
        return True

    def hash_password(self, password):
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()

    def clear_form(self):
        """مسح النموذج"""
        self.username_var.set("")
        self.password_var.set("")
        self.confirm_password_var.set("")
        self.full_name_var.set("")
        self.email_var.set("")
        self.role_var.set("")
        self.is_active_var.set(True)
        self.selected_user_id = None

    def load_users(self):
        """تحميل قائمة المستخدمين"""
        # مسح البيانات الحالية
        for item in self.users_tree.get_children():
            self.users_tree.delete(item)

        # جلب البيانات من قاعدة البيانات
        users_list = self.db_manager.fetch_all("SELECT * FROM users ORDER BY username")

        # إضافة البيانات إلى الجدول
        for user in users_list:
            tag = 'active' if user['is_active'] else 'inactive'
            status = "نشط" if user['is_active'] else "غير نشط"

            self.users_tree.insert("", "end", values=(
                user['id'],
                user['username'],
                user['full_name'],
                user['email'] or "",
                user['role'],
                status,
                user['last_login'] or "لم يسجل دخول"
            ), tags=(tag,))

    def search_users(self, event=None):
        """البحث في المستخدمين"""
        search_term = self.search_var.get().strip()

        if not search_term:
            self.load_users()
            return

        # بناء الاستعلام
        query = """
        SELECT * FROM users
        WHERE username LIKE ? OR full_name LIKE ? OR email LIKE ?
        ORDER BY username
        """
        search_pattern = f"%{search_term}%"
        params = [search_pattern, search_pattern, search_pattern]

        # مسح البيانات الحالية
        for item in self.users_tree.get_children():
            self.users_tree.delete(item)

        # جلب النتائج
        users_list = self.db_manager.fetch_all(query, params)

        # إضافة النتائج إلى الجدول
        for user in users_list:
            tag = 'active' if user['is_active'] else 'inactive'
            status = "نشط" if user['is_active'] else "غير نشط"

            self.users_tree.insert("", "end", values=(
                user['id'],
                user['username'],
                user['full_name'],
                user['email'] or "",
                user['role'],
                status,
                user['last_login'] or "لم يسجل دخول"
            ), tags=(tag,))

    def on_user_select(self, event):
        """عند اختيار مستخدم من القائمة"""
        selection = self.users_tree.selection()
        if selection:
            item = self.users_tree.item(selection[0])
            user_id = item['values'][0]

            # جلب بيانات المستخدم
            user = self.db_manager.fetch_one("SELECT * FROM users WHERE id=?", (user_id,))

            if user:
                # ملء النموذج بالبيانات
                self.username_var.set(user['username'])
                self.full_name_var.set(user['full_name'])
                self.email_var.set(user['email'] or "")
                self.role_var.set(user['role'])
                self.is_active_var.set(user['is_active'])

                # مسح كلمات المرور
                self.password_var.set("")
                self.confirm_password_var.set("")

                self.selected_user_id = user['id']

    def change_password(self):
        """تغيير كلمة مرور المستخدم"""
        if not self.selected_user_id:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم لتغيير كلمة المرور")
            return

        # إنشاء نافذة تغيير كلمة المرور
        password_window = tk.Toplevel(self.parent)
        password_window.title("تغيير كلمة المرور")
        password_window.geometry("400x300")
        password_window.resizable(False, False)

        # متغيرات كلمة المرور الجديدة
        new_password_var = tk.StringVar()
        confirm_new_password_var = tk.StringVar()

        # العنوان
        title_label = ttk_bs.Label(
            password_window,
            text="تغيير كلمة المرور",
            font=("Arial", 14, "bold")
        )
        title_label.pack(pady=20)

        # نموذج كلمة المرور الجديدة
        form_frame = ttk_bs.Frame(password_window)
        form_frame.pack(pady=20, padx=20, fill=X)

        ttk_bs.Label(form_frame, text="كلمة المرور الجديدة:").grid(row=0, column=0, sticky=W, pady=10)
        new_password_entry = ttk_bs.Entry(form_frame, textvariable=new_password_var, show="*", width=30)
        new_password_entry.grid(row=0, column=1, padx=(10, 0), pady=10)

        ttk_bs.Label(form_frame, text="تأكيد كلمة المرور:").grid(row=1, column=0, sticky=W, pady=10)
        confirm_new_password_entry = ttk_bs.Entry(form_frame, textvariable=confirm_new_password_var, show="*", width=30)
        confirm_new_password_entry.grid(row=1, column=1, padx=(10, 0), pady=10)

        # أزرار العمليات
        buttons_frame = ttk_bs.Frame(password_window)
        buttons_frame.pack(pady=20)

        def save_new_password():
            if not new_password_var.get():
                messagebox.showerror("خطأ", "يرجى إدخال كلمة المرور الجديدة")
                return

            if new_password_var.get() != confirm_new_password_var.get():
                messagebox.showerror("خطأ", "كلمة المرور وتأكيدها غير متطابقتين")
                return

            try:
                password_hash = self.hash_password(new_password_var.get())
                query = "UPDATE users SET password_hash=? WHERE id=?"
                self.db_manager.execute_query(query, (password_hash, self.selected_user_id))

                messagebox.showinfo("نجح", "تم تغيير كلمة المرور بنجاح")
                password_window.destroy()

            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في تغيير كلمة المرور: {str(e)}")

        save_btn = ttk_bs.Button(
            buttons_frame,
            text="💾 حفظ",
            bootstyle="success",
            command=save_new_password
        )
        save_btn.pack(side=LEFT, padx=5)

        cancel_btn = ttk_bs.Button(
            buttons_frame,
            text="❌ إلغاء",
            bootstyle="secondary",
            command=password_window.destroy
        )
        cancel_btn.pack(side=LEFT, padx=5)

    def manage_permissions(self):
        """إدارة الصلاحيات"""
        messagebox.showinfo("قريباً", "ميزة إدارة الصلاحيات ستكون متاحة قريباً")

    def show_activity_log(self):
        """عرض سجل النشاط"""
        messagebox.showinfo("قريباً", "ميزة سجل النشاط ستكون متاحة قريباً")

    def load_data(self):
        """تحميل البيانات الأولية"""
        self.load_users()
